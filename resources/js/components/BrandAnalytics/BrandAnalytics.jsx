import React, { useState } from "react";
import DashboardHeader from "../Dashboard/DashboardHeader";
import { <PERSON><PERSON><PERSON>, Line, XAxis, <PERSON>A<PERSON>s, CartesianGrid, <PERSON><PERSON><PERSON>, Legend, ResponsiveContainer } from "recharts";
import { Search, Calendar, BarChart2, ArrowLeft, MoreHorizontal, Plus } from "lucide-react";
import "./BrandAnalytics.css";

const BrandAnalytics = () => {
  const [activeTimeframe, setActiveTimeframe] = useState("7D");

  // Sample data for the chart
  const chartData = [
    { day: "01", date: "Aug", visit_website: 2100, sign_in: 1700, cta_click: 300 },
    { day: "02", date: "Aug", visit_website: 2000, sign_in: 1000, cta_click: 250 },
    { day: "03", date: "Aug", visit_website: 2200, sign_in: 1100, cta_click: 200 },
    { day: "04", date: "Aug", visit_website: 2800, sign_in: 1300, cta_click: 350 },
    { day: "05", date: "Aug", visit_website: 2300, sign_in: 1200, cta_click: 280 },
    { day: "06", date: "Aug", visit_website: 2400, sign_in: 1100, cta_click: 300 },
    { day: "07", date: "Aug", visit_website: 2100, sign_in: 1400, cta_click: 320 },
  ];

  // Sample data for the table
  const tableData = [
    { id: 1, event: "visit_website", avg: "2,400", aug01: "1,760", aug02: "2,847", aug03: "2,406", aug04: "1,909", aug05: "2,509" },
    { id: 2, event: "sign_in", avg: "1,100", aug01: "1,607", aug02: "946", aug03: "1,027", aug04: "952", aug05: "1,252" },
    { id: 3, event: "cta_click", avg: "300", aug01: "167", aug02: "201", aug03: "251", aug04: "127", aug05: "169" },
  ];

  // Event items in the left sidebar
  const eventItems = [
    { id: "C", name: "cta_click" },
    { id: "B", name: "sign_in" },
    { id: "A", name: "visit_website" },
  ];

  return (
    <div className="dashboard">
      <link
        rel="stylesheet"
        href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@latest/dist/tabler-icons.min.css"
      />

      <DashboardHeader />

      <div className="brand-analytics-container">
        <div className="brand-header">
          <div className="brand-header-left">
            <button className="back-button">
              <ArrowLeft size={16} />
            </button>
            <div>
              <h1 className="brand-title">Brand X Microsite Overview</h1>
              <p className="brand-description">
                An insights report is a comprehensive document that provides in-depth analysis
                and valuable information on a specific topic, issue, or dataset
              </p>
            </div>
          </div>
          <div className="brand-header-right">
            <div className="brand-selector">
              <Plus size={16} />
              <span>Brand X</span>
            </div>
            <button className="more-options">
              <MoreHorizontal size={20} />
            </button>
            <button className="save-button">Save</button>
          </div>
        </div>

        <div className="brand-content">
          <div className="brand-sidebar">
            <h2 className="sidebar-title">Analyze by <span className="event-text">Event</span></h2>

            <div className="event-tabs">
              <button className="event-tab active">
                <i className="ti ti-chart-bar"></i>
                <span>Event</span>
              </button>
              <button className="event-tab">
                <i className="ti ti-chart-dots"></i>
                <span>Funnel</span>
              </button>
              <button className="event-tab">
                <i className="ti ti-arrows-right-left"></i>
                <span>Flow</span>
              </button>
            </div>

            <div className="sidebar-section">
              <div className="section-header">
                <h3>Event on Page</h3>
                <button className="add-button">
                  <Plus size={16} />
                </button>
              </div>

              <div className="event-items">
                {eventItems.map(item => (
                  <div key={item.id} className="event-item">
                    <div className="event-icon" style={{ backgroundColor: "#000067" }}>
                      {item.id}
                    </div>
                    <span className="event-name">{item.name}</span>
                    <button className="event-more">
                      <i className="ti ti-dots-vertical"></i>
                    </button>
                  </div>
                ))}
              </div>
            </div>

            <div className="sidebar-section">
              <div className="section-header">
                <h3>Formulas</h3>
                <button className="add-button">
                  <Plus size={16} />
                </button>
              </div>
            </div>

            <div className="sidebar-section">
              <div className="section-header">
                <h3>Filter</h3>
                <button className="add-button">
                  <Plus size={16} />
                </button>
              </div>
            </div>
          </div>

          <div className="brand-main">
            <div className="timeframe-selector">
              <div className="timeframe-options">
                <button className={`timeframe-option ${activeTimeframe === "Today" ? "active" : ""}`}>
                  Today
                </button>
                <button className={`timeframe-option ${activeTimeframe === "Yesterday" ? "active" : ""}`}>
                  Yesterday
                </button>
                <button className={`timeframe-option ${activeTimeframe === "7D" ? "active" : ""}`}>
                  7 D
                </button>
                <button className={`timeframe-option ${activeTimeframe === "30D" ? "active" : ""}`}>
                  30 D
                </button>
                <button className={`timeframe-option ${activeTimeframe === "3M" ? "active" : ""}`}>
                  3 M
                </button>
                <button className={`timeframe-option ${activeTimeframe === "6M" ? "active" : ""}`}>
                  6 M
                </button>
                <button className={`timeframe-option ${activeTimeframe === "12M" ? "active" : ""}`}>
                  12 M
                </button>
              </div>

              <div className="timeframe-actions">
                <button className="custom-button">Custom</button>
                <button className="calendar-button">
                  <Calendar size={16} />
                </button>
              </div>
            </div>

            <div className="chart-controls">
              <button className="compare-button">
                <span>Compare</span>
              </button>

              <div className="chart-view-options">
                <button className="chart-view-option active">
                  <BarChart2 size={16} />
                </button>
                <button className="chart-view-option">
                  <i className="ti ti-chart-bar"></i>
                </button>
              </div>
            </div>

            <div className="chart-container">
              <ResponsiveContainer width="100%" height={300}>
                <LineChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" vertical={false} />
                  <XAxis
                    dataKey="day"
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12 }}
                  />
                  <YAxis
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12 }}
                    domain={[0, 'dataMax + 500']}
                  />
                  <Tooltip />
                  <Line
                    type="monotone"
                    dataKey="visit_website"
                    stroke="#000067"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="sign_in"
                    stroke="#0047BB"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                  <Line
                    type="monotone"
                    dataKey="cta_click"
                    stroke="#00A3E0"
                    strokeWidth={2}
                    dot={{ r: 4 }}
                    activeDot={{ r: 6 }}
                  />
                </LineChart>
              </ResponsiveContainer>

              <div className="chart-legend">
                <div className="legend-item">
                  <div className="legend-dot" style={{ backgroundColor: "#000067" }}></div>
                  <span>visit_website</span>
                </div>
                <div className="legend-item">
                  <div className="legend-dot" style={{ backgroundColor: "#0047BB" }}></div>
                  <span>sign_in</span>
                </div>
                <div className="legend-item">
                  <div className="legend-dot" style={{ backgroundColor: "#00A3E0" }}></div>
                  <span>cta_click</span>
                </div>
              </div>
            </div>

            <div className="table-section">
              <div className="table-header">
                <h3>Detailed List <span className="event-count">3 Events on page</span></h3>
                <div className="table-search">
                  <Search size={16} />
                  <input type="text" placeholder="Search" />
                </div>
              </div>

              <div className="table-container">
                <table className="analytics-table">
                  <thead>
                    <tr>
                      <th>No.</th>
                      <th>Events on page</th>
                      <th>Avg</th>
                      <th>Aug 01</th>
                      <th>Aug 02</th>
                      <th>Aug 03</th>
                      <th>Aug 04</th>
                      <th>Aug 05</th>
                    </tr>
                  </thead>
                  <tbody>
                    {tableData.map(row => (
                      <tr key={row.id}>
                        <td>{row.id}</td>
                        <td>{row.event}</td>
                        <td>{row.avg}</td>
                        <td>{row.aug01}</td>
                        <td>{row.aug02}</td>
                        <td>{row.aug03}</td>
                        <td>{row.aug04}</td>
                        <td>{row.aug05}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BrandAnalytics;


