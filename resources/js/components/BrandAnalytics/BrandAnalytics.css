.brand-analytics-container {
  padding: 0 1.5rem 2rem;
  background-color: #f8fafc;
}

/* Brand Header */
.brand-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 1.5rem 0;
  border-bottom: 1px solid #e5e7eb;
}

.brand-header-left {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 50%;
  cursor: pointer;
}

.brand-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin: 0 0 0.5rem 0;
}

.brand-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  max-width: 600px;
}

.brand-header-right {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.brand-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px dashed #9ca3af;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: #4b5563;
  cursor: pointer;
}

.more-options {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  cursor: pointer;
}

.save-button {
  padding: 0.5rem 1.5rem;
  background-color: #000067;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
}

/* Brand Content Layout */
.brand-content {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 1.5rem;
  margin-top: 1.5rem;
}

/* Sidebar */
.brand-sidebar {
  background-color: #f9fafb;
  border-radius: 0.5rem;
  padding: 1.5rem;
}

.sidebar-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin: 0 0 1.25rem 0;
}

.event-text {
  color: #000067;
}

.event-tabs {
  display: flex;
  gap: 0.25rem;
  margin-bottom: 2rem;
  border-bottom: none;
  padding-bottom: 0;
}

.event-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1rem;
  background-color: transparent;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  cursor: pointer;
}

.event-tab.active {
  background-color: #000067;
  color: white;
}

.sidebar-section {
  margin-bottom: 1.5rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
}

.section-header h3 {
  font-size: 1rem;
  font-weight: 500;
  color: #4b5563;
  margin: 0;
}

.add-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  background-color: transparent;
  border: none;
  cursor: pointer;
  color: #6b7280;
}

.event-items {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.event-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  width: 100%;
}

.event-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 0.25rem;
  background-color: #000067;
  color: white;
  font-weight: 600;
  font-size: 0.875rem;
  margin-right: 0.75rem;
}

.event-name {
  flex: 1;
  font-size: 0.875rem;
  color: #111827;
}

.event-more {
  background: none;
  border: none;
  color: #6b7280;
  cursor: pointer;
  font-size: 1.25rem;
}

/* Main Content */
.brand-main {
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.5rem;
  padding: 1.5rem;
}

.timeframe-selector {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.timeframe-options {
  display: flex;
  gap: 0.5rem;
}

.timeframe-option {
  padding: 0.375rem 0.75rem;
  background-color: transparent;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: #4b5563;
  cursor: pointer;
}

.timeframe-option.active {
  background-color: #eef2ff;
  color: #000067;
  font-weight: 500;
}

.timeframe-actions {
  display: flex;
  gap: 0.5rem;
}

.custom-button {
  padding: 0.375rem 0.75rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: #4b5563;
  cursor: pointer;
}

.calendar-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  cursor: pointer;
}

.chart-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.compare-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.375rem 0.75rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: #4b5563;
  cursor: pointer;
}

.chart-view-options {
  display: flex;
  gap: 0.25rem;
}

.chart-view-option {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  cursor: pointer;
  color: #6b7280;
}

.chart-view-option.active {
  background-color: #eef2ff;
  color: #000067;
  border-color: #c7d2fe;
}

.chart-container {
  margin-bottom: 2rem;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 1.5rem;
  margin-top: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  color: #4b5563;
}

.legend-dot {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
}

.table-section {
  margin-top: 2rem;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.table-header h3 {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  margin: 0;
}

.event-count {
  font-size: 0.875rem;
  font-weight: normal;
  color: #6b7280;
  margin-left: 0.5rem;
}

.table-search {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  width: 240px;
}

.table-search input {
  border: none;
  background: transparent;
  outline: none;
  font-size: 0.875rem;
  color: #4b5563;
  width: 100%;
}

.analytics-table {
  width: 100%;
  border-collapse: collapse;
}

.analytics-table th {
  text-align: left;
  padding: 0.75rem 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: #6b7280;
  border-bottom: 1px solid #e5e7eb;
}

.analytics-table td {
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  color: #111827;
  border-bottom: 1px solid #e5e7eb;
}

.analytics-table tr:last-child td {
  border-bottom: none;
}

.analytics-table tr:hover {
  background-color: #f9fafb;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .brand-content {
    grid-template-columns: 1fr;
  }
  
  .brand-sidebar {
    margin-bottom: 1.5rem;
  }
}

@media (max-width: 768px) {
  .brand-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .brand-header-right {
    width: 100%;
    justify-content: flex-end;
  }
  
  .timeframe-selector {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .timeframe-options {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }
  
  .table-container {
    overflow-x: auto;
  }
}

