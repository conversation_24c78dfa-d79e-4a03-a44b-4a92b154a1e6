import React from "react";
import { <PERSON>, useNavigate } from "react-router";
import { Search, Bell, HelpCircle, ChevronLeft } from "lucide-react";
import "./Header.css";
import profileimg from "../../assets/images/profile.png";

const Header = ({ title, subtitle }) => {
  const navigate = useNavigate();

  return (
    <div className="dashboard-layout">
      {/* Top Navigation */}
      <div className="top-nav">
        <div className="top-nav-left">
          <div className="logo">
            <Link to="/dashboard">
              <img src="/pfizer-logo.png" alt="Pfizer Logo" className="logo-img" />
            </Link>
          </div>
          <nav className="main-nav">
            <Link to="/dashboard" className="nav-item active">Dashboards</Link>
            <Link to="/data-sources" className="nav-item">Data Sources</Link>
            <Link to="/users" className="nav-item">Users</Link>
            <Link to="/dashboard" className="nav-item">More</Link>
          </nav>
        </div>
        <div className="top-nav-right">
          <button className="icon-button">
            <Search size={20} color="#6B7280" />
          </button>
          <button className="icon-button">
            <Bell size={20} color="#6B7280" />
          </button>
          <button className="icon-button">
            <HelpCircle size={20} color="#6B7280" />
          </button>
          <Link to="/profile" className="user-avatar">
            <img
              src={profileimg}
              alt="User Profile"
              className="avatar-img"
            />
          </Link>
        </div>
      </div>

      {/* Sub Navigation */}
      <div className="sub-nav">
        <div className="sub-nav-left">
          <button className="back-button" onClick={() => navigate(-1)}>
            <ChevronLeft className="icon" />
          </button>
          <div className="tabs">
            <Link to="/dashboard" className="tab-button active">
              <span className="tab-icon">📊</span>
              Dashboard
            </Link>
            <Link to="/ai-insights" className="tab-button">
              <span className="tab-icon">🧠</span>
              AI Insights
            </Link>
          </div>
        </div>
      </div>

      {/* Page Header */}
      <div className="page-header">
        <div className="page-header-left">
          <h1 className="page-title">{title || "Marketing Team Dashboard"}</h1>
          <p className="page-subtitle">{subtitle || "Focused on brand-level analysis, audience behavior, and content."}</p>
        </div>
        <div className="page-header-right">
          <div className="add-brand-button">
            <span className="add-brand-icon">+</span>
            Add brand
          </div>
          <button className="add-button">
            <span className="add-icon">+</span>
            Add
          </button>
        </div>
      </div>
    </div>
  );
};

export default Header;

