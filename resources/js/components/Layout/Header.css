.dashboard-layout {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
}

/* Top Navigation */
.top-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background-color: white;
}

.top-nav-left, .top-nav-right {
  display: flex;
  align-items: center;
}

.logo {
  margin-right: 2rem;
}

.logo-img {
  height: 2rem;
}

.main-nav {
  display: flex;
  gap: 1.5rem;
}

.nav-item {
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  text-decoration: none;
  padding: 0.5rem 0;
  position: relative;
}

.nav-item.active {
  color: #000067;
  font-weight: 600;
}

.nav-item.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #000067;
}

.icon-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  margin-left: 0.5rem;
  border-radius: 50%;
}

.icon-button:hover {
  background-color: #f3f4f6;
}

.icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #6b7280;
}

.user-avatar {
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  overflow: hidden;
  margin-left: 1rem;
  border: 2px solid #e5e7eb;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Sub Navigation */
.sub-nav {
  display: flex;
  align-items: center;
  padding: 0.75rem 1.5rem;
  background-color: #f9fafb;
}

.sub-nav-left {
  display: flex;
  align-items: center;
}

.back-button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0.5rem;
  margin-right: 1rem;
  border-radius: 50%;
}

.back-button:hover {
  background-color: #f3f4f6;
}

.tabs {
  display: flex;
  gap: 0.5rem;
}

.tab-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: transparent;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  cursor: pointer;
}

.tab-button.active {
  background-color: #eef2ff;
  color: #000067;
}

.tab-icon {
  font-size: 1rem;
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
}

.page-header-left {
  max-width: 60%;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin: 0;
}

.page-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0.5rem 0 0 0;
}

.page-header-right {
  display: flex;
  gap: 0.75rem;
}

.add-brand-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border: 1px dashed #9ca3af;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  color: #4b5563;
  cursor: pointer;
}

.add-brand-icon {
  font-size: 1rem;
  color: #6b7280;
}

.add-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #000067;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
}

.add-icon {
  font-size: 1rem;
}