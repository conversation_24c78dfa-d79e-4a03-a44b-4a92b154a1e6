.ai-insights-container {
  display: flex;
  height: calc(100vh - 60px);
  background-color: #f5f5f5;
}

/* Sidebar Styles */
.ai-insights-sidebar {
  width: 320px;
  background-color: #fff;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: 1px solid #e0e0e0;
  background-color: #fff;
  cursor: pointer;
  margin: 16px 0 0 16px;
}

.conversation-tabs {
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  margin: 16px 0;
}

.conversation-tab {
  padding: 12px 16px;
  font-size: 14px;
  cursor: pointer;
  position: relative;
}

.conversation-tab.active {
  color: #0000cc;
  font-weight: 500;
}

.conversation-tab.active::after {
  content: '';
  position: absolute;
  bottom: -1px;
  left: 0;
  width: 100%;
  height: 2px;
  background-color: #0000cc;
}

.ai-prompt-container {
  margin: 16px;
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 12px;
  display: flex;
  align-items: flex-start;
}

.ai-prompt-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  background-color: #0000cc;
  color: white;
  margin-right: 12px;
}

.star-icon {
  font-size: 16px;
}

.ai-prompt-form {
  flex: 1;
}

.ai-prompt-input {
  width: 100%;
  border: none;
  background: transparent;
  font-size: 14px;
  padding: 8px 0;
  outline: none;
}

.chat-footer {
  margin-top: auto;
  padding: 16px;
  border-top: 1px solid #e0e0e0;
}

.chat-input-container {
  display: flex;
  align-items: center;
  border: 1px solid #e0e0e0;
  border-radius: 20px;
  padding: 8px 16px;
}

.chat-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px;
}

.chat-send-button {
  background: none;
  border: none;
  color: #0000cc;
  cursor: pointer;
  padding: 4px;
}

/* Content Styles */
.ai-insights-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.ai-insights-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 24px;
  background-color: #f0f0ff; /* Light purple background */
  border-bottom: 1px solid #e0e0e0;
}

.header-left {
  display: flex;
  align-items: center;
}

.header-title {
  display: flex;
  align-items: center;
  margin-left: 16px;
}

.dashboard-link {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #666;
  cursor: pointer;
}

.dashboard-link i {
  margin-right: 6px;
}

.header-divider {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #ccc;
  margin: 0 12px;
}

.ai-insights-title {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-weight: 500;
  color: #0000cc;
}

.ai-insights-title i {
  margin-right: 6px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  font-weight: 500;
}

/* Annotate button - light purple */
.action-button:nth-child(1) {
  background-color: #f0f0ff;
  color: #5151d3;
  border: none;
}

/* Share button - dark blue */
.action-button.primary {
  background-color: #000066;
  color: white;
  border: none;
}

/* Generate PPT button - light red */
.action-button:nth-child(3) {
  background-color: #ffebee;
  color: #e53935;
  border: none;
}

/* Generate Excel button - light green */
.action-button:nth-child(4) {
  background-color: #e8f5e9;
  color: #43a047;
  border: none;
}

.ai-report-container {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
  background-color: #fff;
  margin: 16px;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.ai-report-header {
  display: flex;
  align-items: center;
  padding-bottom: 16px;
  border-bottom: 1px solid #e0e0e0;
  margin-bottom: 24px;
  position: relative;
}

.report-logo-container {
  margin-right: 16px;
}

.report-logo {
  width: 40px;
  height: 40px;
}

.report-title-container {
  flex: 1;
}

.report-title {
  font-size: 20px;
  font-weight: 600;
  color: #0000cc;
  margin: 0;
}

.report-date {
  font-size: 14px;
  color: #666;
  margin: 4px 0 0 0;
}

.fullscreen-button {
  position: absolute;
  top: 0;
  right: 0;
  background: none;
  border: none;
  color: #666;
  cursor: pointer;
}

.report-content {
  max-width: 900px;
  margin: 0 auto;
}

.report-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #0000cc;
  margin: 0 0 16px 0;
}

.section-text {
  font-size: 14px;
  line-height: 1.6;
  color: #333;
  margin: 0;
}

.metrics-table, .channel-table {
  width: 100%;
  overflow-x: auto;
}

.metrics-table table, .channel-table table {
  width: 100%;
  border-collapse: collapse;
}

.metrics-table th, .metrics-table td,
.channel-table th, .channel-table td {
  padding: 10px;
  text-align: left;
  border-bottom: 1px solid #e0e0e0;
  font-size: 14px;
}

.metrics-table th, .channel-table th {
  font-weight: 500;
  color: #666;
}

.trend-list {
  list-style-type: none;
  padding: 0;
  margin: 0;
}

.trend-list li {
  padding: 8px 0;
  font-size: 14px;
  line-height: 1.6;
}

.negative {
  color: #e53935;
}

@media (max-width: 1024px) {
  .ai-insights-container {
    flex-direction: column;
    height: auto;
  }
  
  .ai-insights-sidebar {
    width: 100%;
    height: auto;
    border-right: none;
    border-bottom: 1px solid #e0e0e0;
  }
  
  .ai-report-container {
    margin: 16px;
  }
}


