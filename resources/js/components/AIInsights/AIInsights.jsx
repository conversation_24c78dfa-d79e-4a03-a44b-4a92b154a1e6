import React, { useState } from "react";
import { useNavigate } from "react-router";
import { ArrowLeft, Maximize2, Share, MessageSquare, Send } from "lucide-react";
import DashboardHeader from "../Dashboard/DashboardHeader";
import "./AIInsights.css";

const AIInsights = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("AI Conversation");
  const [userQuestion, setUserQuestion] = useState("");
  const [chatMessages, setChatMessages] = useState([]);

  // Sample metrics data for the table
  const metricsData = [
    { metric: "Total Visits", value: "907,292", benchmark: "–" },
    { metric: "Engagement Rate", value: "66.2%", benchmark: "34.0%" },
    { metric: "MVA Visits", value: "45,562", benchmark: "7.7% conversion" },
    { metric: "HVA Visits", value: "31,424", benchmark: "1.2% conversion" },
    { metric: "Paid Traffic Share", value: "71.1% avg", benchmark: "–" },
    { metric: "Form Completion Rate", value: "88.8%", benchmark: "0.7%" },
    { metric: "Top Channel: Organic Search", value: "86.9% Eng.", benchmark: "Best performance" },
    { metric: "Top Page: Co-Pay Programs", value: "81.4% QVR", benchmark: "Highest converter" },
  ];

  // Channel analysis data
  const channelData = [
    { channel: "Organic Search", visits: "21.4%", engagement: "86.9%", qvr: "26.4%", formSubmission: "39.4%" },
    { channel: "Direct", visits: "23.2%", engagement: "69.8%", qvr: "24.2%", formSubmission: "42.5%" },
  ];

  const handleBackClick = () => {
    navigate(-1);
  };

  const handleTabClick = (tab) => {
    setActiveTab(tab);
  };

  const handleQuestionSubmit = (e) => {
    e.preventDefault();
    // Handle AI question submission
    console.log("Question submitted:", userQuestion);
    setUserQuestion("");
  };

  return (
    <div className="dashboard">
      <link
        rel="stylesheet"
        href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@latest/dist/tabler-icons.min.css"
      />

      <DashboardHeader />

      <div className="ai-insights-container">
        <div className="ai-insights-sidebar">
          <button className="back-button" onClick={handleBackClick}>
            <ArrowLeft size={16} />
          </button>

          <div className="conversation-tabs">
            <div
              className={`conversation-tab ${activeTab === "AI Conversation" ? "active" : ""}`}
              onClick={() => handleTabClick("AI Conversation")}
            >
              AI Conversation
            </div>
            <div
              className={`conversation-tab ${activeTab === "Previous Conversation" ? "active" : ""}`}
              onClick={() => handleTabClick("Previous Conversation")}
            >
              Previous Conversation
            </div>
          </div>

          <div className="ai-prompt-container">
            <div className="ai-prompt-icon">
              <span className="star-icon">★</span>
            </div>
            <form onSubmit={handleQuestionSubmit} className="ai-prompt-form">
              <p class="mb-0">
                Start typing your question... e.g., What's driving low form completion rates?
              </p>
            </form>
          </div>

          <div className="chat-footer">
            <div className="chat-input-container">
              <input
                type="text"
                className="chat-input"
                placeholder="Chat for more insight about the report"
              />
              <button className="chat-send-button">
                <Send size={16} />
              </button>
            </div>
          </div>
        </div>

        <div className="ai-insights-content">
          <div className="ai-insights-header">
            <div className="header-left">
              <button className="back-button" onClick={handleBackClick}>
                <ArrowLeft size={16} />
              </button>
              <div className="header-title">
                <span className="dashboard-link">
                  <i className="ti ti-layout-dashboard"></i> Dashboard
                </span>
                <span className="header-divider"></span>
                <span className="ai-insights-title">
                  <i className="ti ti-brain"></i> AI Insights
                </span>
              </div>
            </div>
            <div className="header-actions">
              <button className="action-button">Annotate</button>
              <button className="action-button primary">
                <Share size={14} />
                Share
              </button>
              <button className="action-button">
                <i className="ti ti-presentation"></i>
                Generate PPT
              </button>
              <button className="action-button">
                <i className="ti ti-table"></i>
                Generate Excel
              </button>
            </div>
          </div>

          <div className="ai-report-container">
            <div className="ai-report-header">
              <div className="report-logo-container">
                <img
                  src="https://cdn.builder.io/api/v1/image/assets/TEMP/ff79270abae4069c92179445e6e8601c1e5c3f7c?placeholderIfAbsent=true"
                  alt="Pfizer Logo"
                  className="report-logo"
                />
              </div>
              <div className="report-title-container">
                <h1 className="report-title">Brand X - Monthly Performance Summary Report</h1>
                <p className="report-date">October 2024</p>
              </div>
              <button className="fullscreen-button">
                <Maximize2 size={16} />
              </button>
            </div>

            <div className="report-content">
              <section className="report-section">
                <h2 className="section-title">Executive Summary</h2>
                <p className="section-text">
                  Brand X demonstrated strong engagement performance (66.2%) and above-benchmark form
                  completion (88.8%), even amid a 77% drop in total traffic. The Co-Pay Programs page remains the
                  primary driver of value actions, with consistent MVA/HVA ratios despite declining volume. The
                  homepage underperformed, and Display/Video ads showed weak ROI, suggesting optimization is
                  needed.
                </p>
              </section>

              <section className="report-section">
                <h2 className="section-title">Key KPIs</h2>
                <div className="metrics-table">
                  <table>
                    <thead>
                      <tr>
                        <th>Metric</th>
                        <th>Value</th>
                        <th>Benchmark</th>
                      </tr>
                    </thead>
                    <tbody>
                      {metricsData.map((item, index) => (
                        <tr key={index}>
                          <td>{item.metric}</td>
                          <td>{item.value}</td>
                          <td>{item.benchmark}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </section>

              <section className="report-section">
                <h2 className="section-title">Traffic & Engagement Trends</h2>
                <ul className="trend-list">
                  <li>Visits declined from 185K in early Sept to 28K in late Oct (<span className="negative">~82%</span>)</li>
                  <li>Engagement improved from 50.3% to 68.3%</li>
                  <li>Organic traffic quality surpassed Paid (Organic QVR: 26.4%; Paid: 16.5%)</li>
                  <li>Device performance shows mobile dominance (71%) but desktop leads in engagement (76%)</li>
                </ul>
              </section>

              <section className="report-section">
                <h2 className="section-title">Channel Analysis</h2>
                <div className="channel-table">
                  <table>
                    <thead>
                      <tr>
                        <th>Channel</th>
                        <th>Visits</th>
                        <th>Engagement</th>
                        <th>QVR</th>
                        <th>Form Submission Share</th>
                      </tr>
                    </thead>
                    <tbody>
                      {channelData.map((item, index) => (
                        <tr key={index}>
                          <td>{item.channel}</td>
                          <td>{item.visits}</td>
                          <td>{item.engagement}</td>
                          <td>{item.qvr}</td>
                          <td>{item.formSubmission}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </section>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIInsights;




