import React from "react";
import { useNavigate } from "react-router";
import DashboardHeader from "../Dashboard/DashboardHeader";
import "./Users.css";
import userimg from "../../assets/images/users.png";
import activeuserimg from "../../assets/images/active-users.png";
import newuserimg from "../../assets/images/new-user.png";


const Users = () => {
  const navigate = useNavigate();

  const handleEditUser = (userId) => {
    navigate(`/user/${userId}`);
  };

  const usersData = [
    { id: 1, name: "<PERSON>", email: "<PERSON><PERSON><PERSON>@pfizer.com", type: "Marketing Team", date: "10/07/2023", status: "Active" },
    { id: 2, name: "<PERSON>", email: "Carlos<PERSON><PERSON><PERSON>@pfizer.com", type: "Leadership Team", date: "24/07/2023", status: "Active" },
    { id: 3, name: "<PERSON>", email: "<PERSON><PERSON><PERSON><PERSON>@pfizer.com", type: "Leadership Team", date: "08/08/2023", status: "Inactive" },
    { id: 4, name: "<PERSON>", email: "<PERSON><PERSON>row<PERSON>@pfizer.com", type: "Leadership Team", date: "31/08/2023", status: "Active" },
    { id: 5, name: "Ryan <PERSON>", email: "<EMAIL>", type: "Media Team", date: "01/05/2023", status: "Inactive" },
    { id: 6, name: "Hailey Adams", email: "<EMAIL>", type: "Leadership Team", date: "10/06/2023", status: "Active" },
    { id: 7, name: "Ashley Anderson", email: "<EMAIL>", type: "Marketing Team", date: "20/02/2023", status: "Active" },
    { id: 8, name: "Emma Wilson", email: "<EMAIL>", type: "Leadership Team", date: "04/09/2021", status: "Active" },
    { id: 9, name: "Hailey Adams", email: "<EMAIL>", type: "Media Team", date: "07/04/2022", status: "Inactive" },
  ];

  return (
    <div className="dashboard">
      <link
        rel="stylesheet"
        href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@latest/dist/tabler-icons.min.css"
      />

      <DashboardHeader />

      <div className="dashboard-content">
        <div className="users-header">
          <h1 className="page-title">Users</h1>
          <div className="users-actions">
            <button className="export-btn">
              <i className="ti ti-download"></i>
              Export
            </button>
            <button className="filter-btn">
              <i className="ti ti-filter"></i>
              Filter
            </button>
            <button className="add-user-btn">
              <i className="ti ti-plus"></i>
              Add User
            </button>
          </div>
        </div>

        <div className="users-overview">
          <h2 className="section-title">Overview</h2>
          <div className="stats-cards">
            <div className="stat-card">
              <div className="stat-info">
                <h3 className="stat-title">Users</h3>
                <div className="stat-value">
                  <span className="number">2,490</span>
                  <span className="change positive">5%</span>
                </div>
              </div>
              <div className="stat-chart">
                <img src={userimg} alt="" srcset="" />
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-info">
                <h3 className="stat-title">Active Users</h3>
                <div className="stat-value">
                  <span className="number">1,980</span>
                  <span className="change negative">25%</span>
                </div>
              </div>
              <div className="stat-chart">
                <img src={activeuserimg} alt="" srcset="" />
              </div>
            </div>
            <div className="stat-card">
              <div className="stat-info">
                <h3 className="stat-title">New User</h3>
                <div className="stat-value">
                  <span className="number">490</span>
                  <span className="change positive">12%</span>
                </div>
              </div>
              <div className="stat-chart">
                <img src={newuserimg} alt="" srcset="" />
              </div>
            </div>
          </div>
        </div>

        <div className="users-table-container">
          <div className="table-header">
            <div className="table-count">2,490 users</div>
            <div className="table-actions">
              <div className="search-box">
                <i className="ti ti-search"></i>
                <input type="text" placeholder="Search" />
              </div>
              <button className="manage-columns-btn">
                <i className="ti ti-layout-columns"></i>
                Manage Columns
              </button>
            </div>
          </div>

          <table className="users-table">
            <thead>
              <tr>
                <th className="checkbox-column">
                  <input type="checkbox" />
                </th>
                <th>Users Name</th>
                <th>Email</th>
                <th>User Type</th>
                <th>Update Date</th>
                <th>Status</th>
                <th>Actions</th>
              </tr>
            </thead>
            <tbody>
              {usersData.map((user) => (
                <tr key={user.id}>
                  <td className="checkbox-column">
                    <input type="checkbox" />
                  </td>
                  <td className="user-name-cell">
                    <div className="user-avatar">{user.name.charAt(0)}</div>
                    <span>{user.name}</span>
                  </td>
                  <td>{user.email}</td>
                  <td>{user.type}</td>
                  <td>{user.date}</td>
                  <td>
                    <span className={`status-badge ${user.status.toLowerCase()}`}>
                      {user.status}
                    </span>
                  </td>
                  <td>
                    <button
                      className="edit-btn"
                      onClick={() => handleEditUser(user.id)}
                    >
                      <i className="ti ti-pencil"></i>
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>

          <div className="pagination">
            <button className="pagination-arrow prev">
              <i className="ti ti-chevron-left"></i>
            </button>
            <div className="pagination-pages">
              <button className="page-number active">1</button>
              <button className="page-number">2</button>
              <button className="page-number">3</button>
              <button className="page-number">4</button>
              <span className="page-ellipsis">...</span>
              <button className="page-number">10</button>
              <button className="page-number">11</button>
            </div>
            <button className="pagination-arrow next">
              <i className="ti ti-chevron-right"></i>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Users;
