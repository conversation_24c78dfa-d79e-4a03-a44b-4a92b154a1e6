/* User Detail Page Styles */

.user-detail-header {
  display: flex;
  align-items: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 50%;
  cursor: pointer;
}

.back-button:hover {
  background-color: #f3f4f6;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin: 0;
}

.user-detail-content {
  display: grid;
  grid-template-columns: 360px 1fr;
  gap: 1.5rem;
}

/* User Profile Section */
.user-profile-section {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.user-profile-card {
  background-color: #f9fafb;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.user-profile-header {
  display: flex;
  align-items: center;
  position: relative;
}

.user-avatar-container {
  margin-right: 1rem;
}

.user-avatar {
  width: 3rem;
  height: 3rem;
  border-radius: 50%;
  object-fit: cover;
}

.user-info {
  flex-grow: 1;
}

.user-name {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 0.25rem 0;
}

.user-email {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
}

.edit-profile-button {
  width: 1.75rem;
  height: 1.75rem;
  background-color: #000067;
  color: white;
  border: none;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.875rem;
}

.user-details-card {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 1rem 0;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #6b7280;
  font-size: 0.875rem;
}

.detail-label i {
  color: #000067;
  font-size: 1.125rem;
}

.detail-value {
  font-size: 0.875rem;
  font-weight: 500;
  color: #111827;
}

.status-badge {
  display: inline-block;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-badge.active {
  background-color: #dcfce7;
  color: #16a34a;
}

/* Activity Log Section */
.activity-log-section {
  background-color: white;
  border-radius: 0.5rem;
  padding: 1.5rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.activity-title h2 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 0.25rem 0;
}

.activity-subtitle {
  font-size: 0.875rem;
  color: #6b7280;
}

.highlight {
  color: #000067;
  font-weight: 500;
  margin-left: 0.25rem;
}

.activity-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.timeframe-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: #111827;
  cursor: pointer;
}

.view-report-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: #111827;
  cursor: pointer;
}

.view-report-button i {
  color: #000067;
}

.activity-timeline {
  display: flex;
  flex-direction: column;
}

.activity-item {
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  position: relative;
}

.activity-icon-container {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.activity-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  margin-bottom: 0.5rem;
}

.activity-icon i {
  font-size: 1.25rem;
}

.timeline-line {
  flex-grow: 1;
  width: 2px;
  background-color: #e5e7eb;
}

.activity-content {
  flex-grow: 1;
}

.activity-main {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.activity-info {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.activity-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #111827;
}

.activity-time {
  font-size: 0.75rem;
  color: #6b7280;
}

.product-code {
  font-size: 0.75rem;
  color: #6b7280;
  margin-bottom: 0.25rem;
}

.detail-button {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background-color: #f3f4f6;
  border: none;
  padding: 0.375rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  color: #000067;
  cursor: pointer;
  align-self: flex-start;
}

.detail-button.view {
  background-color: #f0f1ff;
}

.detail-button.hide {
  background-color: #f0f1ff;
}

.brand-info {
  display: flex;
  align-items: center;
  background-color: #f9fafb;
  border-radius: 0.375rem;
  padding: 0.75rem;
  margin-top: 0.5rem;
}

.brand-image-container {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 0.25rem;
  overflow: hidden;
  background-color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 0.75rem;
}

.brand-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.brand-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #111827;
  flex-grow: 1;
}

.product-link-container {
  margin-left: auto;
}

.product-link {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  font-size: 0.75rem;
  color: #000067;
  text-decoration: none;
}

.product-link:hover {
  text-decoration: underline;
}




