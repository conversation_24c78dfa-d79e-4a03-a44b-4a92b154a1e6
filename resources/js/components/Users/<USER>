import React, { useState } from 'react';
import DashboardHeader from '../Dashboard/DashboardHeader';
import { ChevronLeft, Calendar, ExternalLink } from 'lucide-react';
import './UserDetail.css';

const UserDetail = () => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('Last 7 days');

  // Activity logs data
  const activityLogs = [
    {
      id: 1,
      type: 'brand_profit',
      icon: 'shopping-cart',
      iconBg: '#e6f7ff',
      iconColor: '#0077cc',
      time: '14:00',
      date: 'Yesterday',
      productCode: '2923402349',
      hasDetail: true,
      detailType: 'view'
    },
    {
      id: 2,
      type: 'cta_click',
      icon: 'click',
      iconBg: '#e6f0ff',
      iconColor: '#3366ff',
      time: '14:30',
      date: 'Sep 28, 2022',
      productCode: '2923402349',
      hasDetail: true,
      detailType: 'hide',
      brandImage: '/images/brand2.png',
      brandName: 'Brand Name'
    },
    {
      id: 3,
      type: 'visit_website',
      icon: 'world',
      iconBg: '#e6f9ff',
      iconColor: '#00b8d9',
      time: '14:30',
      date: 'Sep 28, 2022',
      productCode: '2923402349',
      hasDetail: true,
      detailType: 'hide',
      brandImage: '/images/brand3.png',
      brandName: 'Brand Name'
    },
    {
      id: 4,
      type: 'sign_in',
      icon: 'login',
      iconBg: '#eeeeff',
      iconColor: '#5555ff',
      time: '14:30',
      date: 'Sep 28, 2022',
      hasDetail: true,
      detailType: 'view'
    }
  ];

  return (
    <div className="dashboard">
      <link
        rel="stylesheet"
        href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@latest/dist/tabler-icons.min.css"
      />

      <DashboardHeader />

      <div className="dashboard-content">
        <div className="user-detail-header">
          <button className="back-button">
            <ChevronLeft size={20} />
          </button>
          <h1 className="page-title">User Detail</h1>
        </div>

        <div className="user-detail-content">
          <div className="user-profile-section">
            <div className="user-profile-card">
              <div className="user-profile-header">
                <div className="user-avatar-container">
                  <img
                    src="https://randomuser.me/api/portraits/women/44.jpg"
                    alt="Elizabeth Bailey"
                    className="user-avatar"
                  />
                </div>
                <div className="user-info">
                  <h2 className="user-name">Elizabeth Bailey</h2>
                  <p className="user-email"><EMAIL></p>
                </div>
                <button className="edit-profile-button">
                  <i className="ti ti-pencil"></i>
                </button>
              </div>
            </div>

            <div className="user-details-card">
              <h3 className="card-title">Demographic</h3>

              <div className="detail-item">
                <div className="detail-label">
                  <i className="ti ti-circle-check"></i>
                  <span>Status</span>
                </div>
                <div className="detail-value">
                  <span className="status-badge active">Active</span>
                </div>
              </div>

              <div className="detail-item">
                <div className="detail-label">
                  <i className="ti ti-users"></i>
                  <span>User Type</span>
                </div>
                <div className="detail-value">Leadership Team</div>
              </div>

              <div className="detail-item">
                <div className="detail-label">
                  <i className="ti ti-calendar"></i>
                  <span>Update Date</span>
                </div>
                <div className="detail-value">08/08/2023</div>
              </div>

              <div className="detail-item">
                <div className="detail-label">
                  <i className="ti ti-gender-femme"></i>
                  <span>Gender</span>
                </div>
                <div className="detail-value">Female</div>
              </div>

              <div className="detail-item">
                <div className="detail-label">
                  <i className="ti ti-calendar-stats"></i>
                  <span>Age</span>
                </div>
                <div className="detail-value">46</div>
              </div>

              <div className="detail-item">
                <div className="detail-label">
                  <i className="ti ti-language"></i>
                  <span>Language</span>
                </div>
                <div className="detail-value">English</div>
              </div>
            </div>

            <div className="user-details-card">
              <h3 className="card-title">Technology Adoption</h3>

              <div className="detail-item">
                <div className="detail-label">
                  <i className="ti ti-device-laptop"></i>
                  <span>Platform</span>
                </div>
                <div className="detail-value">iOS, OSX</div>
              </div>

              <div className="detail-item">
                <div className="detail-label">
                  <i className="ti ti-device-mobile"></i>
                  <span>Device</span>
                </div>
                <div className="detail-value">iPhone X</div>
              </div>
            </div>
          </div>

          <div className="activity-log-section">
            <div className="activity-header">
              <h2>Activity log</h2>
              <div className="activity-actions">
                <div className="avg-time">
                  <span>Avg Time On Page:</span>
                  <span className="highlight">2m16s</span>
                </div>
                <button className="timeframe-selector">
                  Last 7 days
                  <Calendar size={16} />
                </button>
                <button className="view-report-button">
                  <i className="ti ti-chart-bar"></i>
                  <span>View in User Report</span>
                </button>
              </div>
            </div>

            <div className="activity-timeline">
              {activityLogs.map((activity) => (
                <div key={activity.id} className="activity-item">
                  <div className="activity-icon-container">
                    <div className="activity-icon" style={{ backgroundColor: activity.iconBg }}>
                      <i className={`ti ti-${activity.icon}`} style={{ color: activity.iconColor }}></i>
                    </div>
                    <div className="timeline-line"></div>
                  </div>

                  <div className="activity-content">
                    <div className="activity-main">
                      <div className="activity-info">
                        <div className="activity-name">
                          {activity.type.replace(/_/g, ' ')}
                        </div>
                        <div className="activity-time">
                          {activity.time} - {activity.date}
                        </div>
                      </div>

                      {activity.productCode && (
                        <div className="product-code">
                          Product code: {activity.productCode}
                        </div>
                      )}

                      <button className="detail-button">
                        {activity.detailType === 'hide' ? 'Show less' : 'View detail'}
                        <i className={`ti ti-chevron-${activity.detailType === 'hide' ? 'up' : 'down'}`}></i>
                      </button>
                    </div>

                    {activity.brandName && (
                      <div className="brand-info">
                        <div className="brand-image-container">
                          <img
                            src={activity.brandImage || "https://via.placeholder.com/40"}
                            alt={activity.brandName}
                            className="brand-image"
                          />
                        </div>
                        <div className="brand-name">{activity.brandName}</div>
                        <div className="product-link-container">
                          <a href="#" className="product-link">
                            Go to product page
                            <ExternalLink size={14} />
                          </a>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>

        </div>
      </div>
    </div>
  );
};

export default UserDetail;

