import React, { useState, useRef, useEffect } from "react";
import { useNavigate } from "react-router";
import userImg from "../../assets/images/user.png";
import "./TemplateModal.css";

// Görselleri import edelim
import userAnalysisImg from "../../assets/images/user-analysis.png";
import companyKpiImg from "../../assets/images/company-kpi.png";
import insightfulInteractionsImg from "../../assets/images/insightful-interactions.png";

const templateOptions = [
  {
    id: 1,
    title: "Media Team Dashboard",
    description: "Unlock valuable insights into user behavior and preference...",
    image: userAnalysisImg,
    type: "User Analysis"
  },
  {
    id: 2,
    title: "Marketing Team Dashboard",
    description: "Key Performance Indicators (KPIs) are specific, measurable metrics...",
    image: companyKpiImg,
    type: "Company KPI"
  },
  {
    id: 3,
    title: "Leadership Team Dashboard",
    description: "Evaluating our company's performance is a breeze with our carefully selected...",
    image: insightfulInteractionsImg,
    type: "Insightful Interactions"
  }
];

const suggestedEvents = [
  "visit_website",
  "sign_in",
  "sign_up",
  "fill_form",
  "download_brochure",
  "watch_video",
  "contact_sales",
  "request_demo",
  "add_to_cart",
  "complete_purchase"
];

const suggestedKPIs = [
  "Campaign Performance",
  "Audience Behavior",
  "Content Insights",
  "Conversion Rate",
  "Customer Acquisition Cost",
  "Customer Lifetime Value",
  "Return on Investment",
  "Click-Through Rate",
  "Bounce Rate",
  "Average Session Duration"
];

const TemplateModal = ({ onClose }) => {
  const navigate = useNavigate();
  const [showTemplateForm, setShowTemplateForm] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState(null);
  const [currentStep, setCurrentStep] = useState(1);

  // Form state
  const [dashboardTitle, setDashboardTitle] = useState("");
  const [dashboardDescription, setDashboardDescription] = useState("");
  const [events, setEvents] = useState([]);
  const [kpis, setKpis] = useState([]);
  const [newEvent, setNewEvent] = useState("");
  const [newKpi, setNewKpi] = useState("");
  const [showEventInput, setShowEventInput] = useState(false);
  const [showKpiInput, setShowKpiInput] = useState(false);
  const [showEventDropdown, setShowEventDropdown] = useState(false);
  const [showKpiDropdown, setShowKpiDropdown] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [kpiSearchTerm, setKpiSearchTerm] = useState("");
  const [filteredEvents, setFilteredEvents] = useState(suggestedEvents);
  const [filteredKpis, setFilteredKpis] = useState(suggestedKPIs);
  const [showUserDropdown, setShowUserDropdown] = useState(false);

  const dropdownRef = useRef(null);
  const kpiDropdownRef = useRef(null);
  const searchInputRef = useRef(null);
  const kpiSearchInputRef = useRef(null);

  // Dropdown dışına tıklandığında kapanması için
  useEffect(() => {
    function handleClickOutside(event) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowEventDropdown(false);
      }
      if (kpiDropdownRef.current && !kpiDropdownRef.current.contains(event.target)) {
        setShowKpiDropdown(false);
      }
    }

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Arama yapıldığında filtreleme
  useEffect(() => {
    const filtered = suggestedEvents.filter(event =>
      event.toLowerCase().includes(searchTerm.toLowerCase())
    );
    setFilteredEvents(filtered);
  }, [searchTerm]);

  useEffect(() => {
    const filtered = suggestedKPIs.filter(kpi =>
      kpi.toLowerCase().includes(kpiSearchTerm.toLowerCase())
    );
    setFilteredKpis(filtered);
  }, [kpiSearchTerm]);

  // Dropdown açıldığında search input'a focus
  useEffect(() => {
    if (showEventDropdown && searchInputRef.current) {
      searchInputRef.current.focus();
    }
    if (showKpiDropdown && kpiSearchInputRef.current) {
      kpiSearchInputRef.current.focus();
    }
  }, [showEventDropdown, showKpiDropdown]);

  // Hata yakalama için
  const handleImageError = (e) => {
    // Görsel yüklenemezse varsayılan bir görsel göster
    e.target.src = "https://via.placeholder.com/200x150?text=Dashboard+Template";
  };

  const handleUseTemplate = (template) => {
    setSelectedTemplate(template);
    setDashboardTitle(template.title);
    setDashboardDescription(template.description);
    setShowTemplateForm(true);
  };

  const handleBackToTemplate = () => {
    setShowTemplateForm(false);
  };

  const handleNextStep = () => {
    if (currentStep < 4) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleAddEvent = () => {
    setShowEventDropdown(true);
  };

  const handleAddKpi = () => {
    setShowKpiDropdown(true);
  };

  const handleEventSelect = (event) => {
    setEvents([...events, event]);
    setShowEventDropdown(false);
    setSearchTerm("");
  };

  const handleKpiSelect = (kpi) => {
    setKpis([...kpis, kpi]);
    setShowKpiDropdown(false);
    setKpiSearchTerm("");
  };

  const handleSearchChange = (e) => {
    setSearchTerm(e.target.value);
  };

  const handleKpiSearchChange = (e) => {
    setKpiSearchTerm(e.target.value);
  };

  const handleCustomEvent = () => {
    setShowEventDropdown(false);
    setShowEventInput(true);
  };

  const handleCustomKpi = () => {
    setShowKpiDropdown(false);
    setShowKpiInput(true);
  };

  const handleEventInputChange = (e) => {
    setNewEvent(e.target.value);
  };

  const handleKpiInputChange = (e) => {
    setNewKpi(e.target.value);
  };

  const handleEventInputKeyDown = (e) => {
    if (e.key === 'Enter' && newEvent.trim()) {
      setEvents([...events, newEvent.trim()]);
      setNewEvent("");
      setShowEventInput(false);
    } else if (e.key === 'Escape') {
      setNewEvent("");
      setShowEventInput(false);
    }
  };

  const handleKpiInputKeyDown = (e) => {
    if (e.key === 'Enter' && newKpi.trim()) {
      setKpis([...kpis, newKpi.trim()]);
      setNewKpi("");
      setShowKpiInput(false);
    } else if (e.key === 'Escape') {
      setNewKpi("");
      setShowKpiInput(false);
    }
  };

  const handleEventInputBlur = () => {
    if (newEvent.trim()) {
      setEvents([...events, newEvent.trim()]);
      setNewEvent("");
    }
    setShowEventInput(false);
  };

  const handleKpiInputBlur = () => {
    if (newKpi.trim()) {
      setKpis([...kpis, newKpi.trim()]);
      setNewKpi("");
    }
    setShowKpiInput(false);
  };

  return (
    <div className="modal-overlay">
      {!showTemplateForm ? (
        <div className="template-modal">
          <div className="modal-header">
            <h2>Select Template</h2>
            <button className="close-button" onClick={onClose}>
              <i className="ti ti-x" />
            </button>
          </div>

          <div className="template-options">
            {templateOptions.map((template) => (
              <div key={template.id} className="template-option">
                <div className="template-preview">
                  <img
                    src={template.image}
                    alt={template.title}
                    onError={handleImageError}
                  />
                  <div className="template-type">{template.type}</div>
                </div>

                <div className="template-details">
                  <h3>{template.title}</h3>
                  <p>{template.description}</p>

                  <div className="template-actions">
                    <button
                      className="use-template-btn"
                      onClick={() => handleUseTemplate(template)}
                    >
                      Use template
                    </button>
                    <button className="view-demo-btn">View demo</button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          <div className="modal-footer">
            <button className="show-more-btn">Show more</button>
          </div>
        </div>
      ) : (
        <div className="template-form-modal">
          <div className="modal-header">
            <button className="back-button" onClick={handleBackToTemplate}>
              <i className="ti ti-arrow-left" /> Back to template
            </button>
            <button className="close-button" onClick={onClose}>
              <i className="ti ti-x" />
            </button>
          </div>

          <div className="template-form-content">
            <div className="template-form-steps">
              <div className={`step ${currentStep === 1 ? 'active' : ''}`}>
                <span className="step-number">1.</span> Main Details
              </div>
              <div className={`step ${currentStep === 2 ? 'active' : ''}`}>
                <span className="step-number">2.</span> Applicable KPIs
              </div>
              <div className={`step ${currentStep === 3 ? 'active' : ''}`}>
                <span className="step-number">3.</span> Authorization
              </div>
              <div className={`step ${currentStep === 4 ? 'active' : ''}`}>
                <span className="step-number">4.</span> Saving Options
              </div>
            </div>

            <div className="step-indicator">
              <div className="step-line"></div>
              <div className="step-progress" style={{ width: `${(currentStep - 1) * 33.33}%` }}></div>
            </div>

            {currentStep === 1 && (
              <div className="template-form-step">
                <p>Please associate each of the suggested events with its respective option</p>

                <div className="form-group-gray">
                  <div className="form-group">
                    <input
                      type="text"
                      className="form-control"
                      value={dashboardTitle}
                      onChange={(e) => setDashboardTitle(e.target.value)}
                      placeholder="Dashboard Title"
                    />

                    <div className="brand-selector">
                      <i className="ti ti-target" /> Brand X, Brand Y
                    </div>
                  </div>

                  <div className="form-group mt-3">
                    <textarea
                      className="form-control"
                      value={dashboardDescription}
                      onChange={(e) => setDashboardDescription(e.target.value)}
                      placeholder="Dashboard Description"
                      rows={3}
                    />
                  </div>
                </div>


                <div className="form-section">
                  <div className="section-header">
                    <h4>List of Events</h4>
                    <h4>Value Action</h4>
                  </div>

                  {events.map((event, index) => (
                    <div className="event-row" key={index}>
                      <div className="event-item">
                        <i className="ti ti-plus" /> {event}
                      </div>

                      <div className="value-action">
                        <i className="ti ti-target" /> buy_product
                      </div>
                    </div>
                  ))}

                  <div className="event-row">
                    <div className="event-dropdown-container" ref={dropdownRef}>
                      {showEventInput ? (
                        <div className="event-input-container">
                          <input
                            type="text"
                            className="event-input"
                            value={newEvent}
                            onChange={handleEventInputChange}
                            onKeyDown={handleEventInputKeyDown}
                            onBlur={handleEventInputBlur}
                            placeholder="Enter event name"
                            autoFocus
                          />
                        </div>
                      ) : (
                        <button className="add-event-btn" onClick={handleAddEvent}>
                          <i className="ti ti-plus" /> Add event
                        </button>
                      )}

                      {showEventDropdown && (
                        <div className="event-dropdown">
                          <div className="event-dropdown-search">
                            <i className="ti ti-search search-icon" />
                            <input
                              type="text"
                              className="event-search-input"
                              placeholder="Search"
                              value={searchTerm}
                              onChange={handleSearchChange}
                              ref={searchInputRef}
                            />
                          </div>

                          <div className="event-dropdown-header">
                            <span>30 suggested events</span>
                            <button className="custom-event-btn" onClick={handleCustomEvent}>
                              <i className="ti ti-pencil" /> Custom
                            </button>
                          </div>

                          <div className="event-dropdown-list">
                            {filteredEvents.map((event, index) => (
                              <div
                                key={index}
                                className="event-dropdown-item"
                                onClick={() => handleEventSelect(event)}
                              >
                                <i className="ti ti-arrow-right" /> {event}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="value-action">
                      <i className="ti ti-target" /> buy_product
                    </div>
                  </div>
                </div>

                <div className="form-actions">
                  <button className="next-btn" onClick={handleNextStep}>
                    NEXT
                  </button>
                </div>
              </div>
            )}

            {currentStep === 2 && (
              <div className="template-form-step">
                <p>Please associate each of the suggested KPIs listed by AI according to the selected events and values.</p>

                <div className="kpi-section">
                  {kpis.map((kpi, index) => (
                    <div className="kpi-item" key={index}>
                      <i className="ti ti-chart-bar" /> {kpi}
                    </div>
                  ))}

                  <div className="kpi-dropdown-container" ref={kpiDropdownRef}>
                    {showKpiInput ? (
                      <div className="kpi-input-container">
                        <input
                          type="text"
                          className="kpi-input"
                          value={newKpi}
                          onChange={handleKpiInputChange}
                          onKeyDown={handleKpiInputKeyDown}
                          onBlur={handleKpiInputBlur}
                          placeholder="Enter KPI name"
                          autoFocus
                        />
                      </div>
                    ) : (
                      <button className="add-kpi-btn" onClick={handleAddKpi}>
                        <i className="ti ti-plus" /> Add KPI
                      </button>
                    )}

                    {showKpiDropdown && (
                      <div className="kpi-dropdown">
                        <div className="kpi-dropdown-search">
                          <i className="ti ti-search search-icon" />
                          <input
                            type="text"
                            className="kpi-search-input"
                            placeholder="Search"
                            value={kpiSearchTerm}
                            onChange={handleKpiSearchChange}
                            ref={kpiSearchInputRef}
                          />
                        </div>

                        <div className="kpi-dropdown-header">
                          <span>30 suggested events</span>
                          <button className="custom-kpi-btn" onClick={handleCustomKpi}>
                            <i className="ti ti-pencil" /> Custom
                          </button>
                        </div>

                        <div className="kpi-dropdown-list">
                          {filteredKpis.map((kpi, index) => (
                            <div
                              key={index}
                              className="kpi-dropdown-item"
                              onClick={() => handleKpiSelect(kpi)}
                            >
                              <i className="ti ti-chart-bar" /> {kpi}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                <div className="form-actions">
                  <button className="back-btn" onClick={handlePrevStep}>
                    BACK
                  </button>
                  <button className="next-btn" onClick={handleNextStep}>
                    NEXT
                  </button>
                </div>
              </div>
            )}

            {currentStep === 3 && (
              <div className="template-form-step">
                <p>Please configure the authorization and access settings.</p>

                <div className="authorization-card">
                  <div className="auth-card-header">
                    <h3>Invite Users</h3>
                    <button className="close-auth-btn">
                      <i className="ti ti-x" />
                    </button>
                  </div>

                  <div className="auth-input-row">
                    <input
                      type="text"
                      className="auth-input"
                      placeholder="Enter name or email"
                      onClick={() => setShowUserDropdown(prev => !prev)}
                    />

                    <div className="auth-permission-select">
                      <button className="auth-permission-btn">
                        Can edit <i className="ti ti-chevron-down" />
                      </button>
                    </div>

                    <button className="auth-invite-btn">
                      Send invite
                    </button>
                  </div>

                  {showUserDropdown && (
                    <div className="auth-users-dropdown">
                      <div className="auth-user-item">
                        <div className="auth-user-avatar">
                          <img src={userImg} alt="User" />
                        </div>
                        <div className="auth-user-info">
                          <div className="auth-user-name">User name</div>
                          <div className="auth-user-email"><EMAIL></div>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="auth-user-list">
                    <div className="auth-user-row">
                      <div className="auth-user-info-container">
                        <div className="auth-user-avatar">
                          <img src={userImg} alt="User" />
                        </div>
                        <div className="auth-user-info">
                          <div className="auth-user-name">User name</div>
                          <div className="auth-user-email"><EMAIL></div>
                        </div>
                      </div>
                      <div className="auth-user-role">Owner</div>
                    </div>
                  </div>

                  <div className="auth-divider"></div>

                  <div className="auth-access-row">
                    <div className="auth-access-option">
                      <i className="ti ti-lock" />
                      <span>Only people invited to this file</span>
                    </div>
                    <div className="auth-permission-select">
                      <button className="auth-permission-btn">
                        Can edit <i className="ti ti-chevron-down" />
                      </button>
                    </div>
                  </div>

                  <div className="auth-divider"></div>

                  <div className="auth-link-row">
                    <button className="auth-copy-link-btn">
                      <i className="ti ti-link" /> Copy link
                    </button>
                    <a href="#" className="auth-learn-more">
                      <i className="ti ti-help" /> Learn more about sharing
                    </a>
                  </div>
                </div>

                <div className="form-actions">
                  <button className="back-btn" onClick={handlePrevStep}>
                    BACK
                  </button>
                  <button className="next-btn" onClick={handleNextStep}>
                    NEXT
                  </button>
                </div>
              </div>
            )}

            {currentStep === 4 && (
              <div className="template-form-step">
                <div className="saving-options-container">
                  <h3 className="saving-options-title">Choose how you want your changes to templates and dashboards to be saved.</h3>
                  <p className="saving-options-subtitle">Protect your work, avoid accidental mistakes, and ensure version control.</p>

                  <div className="saving-options-list">
                    <div className="saving-option">
                      <div className="saving-option-radio">
                        <input
                          type="radio"
                          id="manual-save"
                          name="saving-option"
                          defaultChecked
                        />
                        <label htmlFor="manual-save">Manual Save (Recommended)</label>
                      </div>
                      <div className="saving-option-description">
                        <p>Changes are saved in real-time, but you can undo any action or revert to a previous version.</p>
                      </div>
                      <div className="saving-option-users">
                        <p>Leadership, Marketing, and Media teams</p>
                      </div>
                    </div>

                    <div className="saving-option">
                      <div className="saving-option-radio">
                        <input
                          type="radio"
                          id="auto-save"
                          name="saving-option"
                        />
                        <label htmlFor="auto-save">Auto-Save with Undo</label>
                      </div>
                      <div className="saving-option-description">
                        <p>Changes are saved in real-time, but you can undo any action or revert to a previous version.</p>
                      </div>
                      <div className="saving-option-users">
                        <p>Power users familiar with the platform</p>
                      </div>
                    </div>

                    <div className="saving-option">
                      <div className="saving-option-radio">
                        <input
                          type="radio"
                          id="prompt-save"
                          name="saving-option"
                        />
                        <label htmlFor="prompt-save">Prompt Before Save</label>
                      </div>
                      <div className="saving-option-description">
                        <p>The system asks for confirmation after each change. Great for learning or critical dashboards.</p>
                      </div>
                      <div className="saving-option-users">
                        <p>New users or admins updating core templates</p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="form-actions">
                  <button className="back-btn" onClick={handlePrevStep}>
                    BACK
                  </button>
                  <button
                    onClick={() => navigate('/dashboard-overview')}
                    className="complete-btn"
                  >
                    COMPLETE
                  </button>
                </div>
              </div>
            )}

            {/* Diğer adımlar için içerik buraya eklenebilir */}
          </div>
        </div>
      )}
    </div>
  );
};

export default TemplateModal;















