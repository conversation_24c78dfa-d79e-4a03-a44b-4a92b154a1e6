import React from "react";

const templateCards = [
  {
    icon: "ti-clock",
    title: "Media Team Dashboard",
    stats: "1 board • 4 Reports",
  },
  {
    icon: "ti-trending-up",
    title: "Leadership Team Dashboard",
    stats: "1 board • 4 Reports",
  },
  {
    icon: "ti-chart-bar",
    title: "Media Team Dashboard 2",
    stats: "1 board • 4 Reports",
  },
  {
    icon: "ti-chart-line",
    title: "Media Team Dashboard",
    stats: "1 board • 4 Reports",
  },
];

const DashboardTemplates = () => {
  return (
    <div className="templates-section">
      <h2 className="templates-title">Suggested template</h2>
      <div className="templates-grid">
        {templateCards.map((card, index) => (
          <div key={index} className="template-card">
            <div className="template-icon-wrapper">
              <i className={`ti ${card.icon} template-icon`} />
            </div>
            <div className="template-title">{card.title}</div>
            <div className="template-stats">{card.stats}</div>
          </div>
        ))}
        <div className="template-card template-card-more">
          <div className="template-icon-wrapper template-icon-more">
            <i className="ti ti-plus template-icon" />
          </div>
          <div className="template-title-more">More than</div>
          <div className="template-stats">20+ templates</div>
        </div>
      </div>
    </div>
  );
};

export default DashboardTemplates;
