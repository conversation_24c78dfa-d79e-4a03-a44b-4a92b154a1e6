/* Template Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.template-modal {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.25rem;
  color: #64748b;
}

.template-options {
  padding: 16px 24px;
}

.template-option {
  display: flex;
  padding: 24px 0;
  border-bottom: 1px solid #e2e8f0;
}

.template-option:last-child {
  border-bottom: none;
}

.template-preview {
  flex: 0 0 200px;
  margin-right: 24px;
  position: relative;
}

.template-preview img {
  width: 100%;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.template-type {
  position: absolute;
  top: 12px;
  left: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  color: #1e293b;
}

.template-details {
  flex: 1;
  text-align: left;
}

.template-details h3 {
  margin: 0 0 8px;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
}

.template-details p {
  margin: 0 0 16px;
  font-size: 0.875rem;
  color: #64748b;
}

.template-actions {
  display: flex;
  gap: 12px;
}

.use-template-btn {
  background-color: #000067;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
}

.view-demo-btn {
  background-color: transparent;
  color: #000067;
  border: none;
  padding: 8px 16px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
}

/* Template Form Styles */
.template-form-modal {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.template-form-steps {
  display: flex;
  margin-bottom: 16px;
  gap: 24px;
}

.template-form-steps .step {
  font-size: 0.875rem;
  color: #64748b;
  padding-bottom: 8px;
  position: relative;
}

.template-form-steps .step.active {
  color: #000067;
  font-weight: 600;
}

.template-form-steps .step.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #000067;
  border-radius: 2px;
}

.template-form-content {
  padding: 24px;
}

.form-group {
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  color: #1e293b;
}

.form-control {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  font-size: 0.875rem;
  color: #1e293b;
}

.form-control:focus {
  outline: none;
  border-color: #000067;
  box-shadow: 0 0 0 2px rgba(0, 0, 103, 0.1);
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 24px;
}

.back-btn {
  background-color: transparent;
  color: #64748b;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
}

.next-btn, .complete-btn {
  background-color: #000067;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
}

.event-dropdown, .kpi-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  max-height: 200px;
  overflow-y: auto;
  background-color: white;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 10;
}

.dropdown-search {
  padding: 8px 12px;
  border-bottom: 1px solid #e2e8f0;
}

.dropdown-search input {
  width: 100%;
  padding: 8px;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  font-size: 0.875rem;
}

.dropdown-options {
  padding: 8px 0;
}

.dropdown-option {
  padding: 8px 12px;
  cursor: pointer;
  font-size: 0.875rem;
  color: #1e293b;
}

.dropdown-option:hover {
  background-color: #f1f5f9;
}

.custom-option {
  padding: 8px 12px;
  border-top: 1px solid #e2e8f0;
  color: #000067;
  font-weight: 500;
  cursor: pointer;
}

.event-tag, .kpi-tag {
  display: inline-block;
  background-color: #f1f5f9;
  color: #1e293b;
  padding: 4px 8px;
  border-radius: 4px;
  margin-right: 8px;
  margin-bottom: 8px;
  font-size: 0.875rem;
}