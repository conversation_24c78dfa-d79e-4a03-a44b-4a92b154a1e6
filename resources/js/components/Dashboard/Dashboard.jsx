import React, { useState } from "react";
import DashboardHeader from "./DashboardHeader";
import DashboardTemplates from "./DashboardTemplates";
import DashboardTable from "./DashboardTable";
import TemplateModal from "./TemplateModal";
import "../../styles/Dashboard.css";

const Dashboard = () => {
  const [showNotification, setShowNotification] = useState(true);
  const [showTemplateModal, setShowTemplateModal] = useState(false);

  return (
    <div className="dashboard">
      <link
        rel="stylesheet"
        href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@latest/dist/tabler-icons.min.css"
      />

      <DashboardHeader />

      <div className="dashboard-content">
        {showNotification && (
          <div className="notification-banner">
            <div className="notification-message">
              <i className="ti ti-bell notification-icon" />
              <span>
                Discover the power of our SmartRX's features for a hands-on
                experience of its seamless functionality
              </span>
            </div>
            <div className="notification-actions">
              <button className="notification-demo-btn">View demo</button>
              <i
                className="ti ti-x notification-close"
                onClick={() => setShowNotification(false)}
              />
            </div>
          </div>
        )}

        <div className="dashboard-header">
          <h1 className="dashboard-title">Dashboards</h1>
          <button
            className="create-dashboard-btn"
            onClick={() => setShowTemplateModal(true)}
          >
            <i className="ti ti-plus" />
            <span>Create New Dashboard</span>
          </button>
        </div>

        <DashboardTemplates />
        <DashboardTable />
      </div>

      {showTemplateModal && (
        <TemplateModal onClose={() => setShowTemplateModal(false)} />
      )}
    </div>
  );
};

export default Dashboard;

