import React from "react";
import { <PERSON>, useNavigate } from "react-router";
import { Search, Bell, HelpCircle, User } from "lucide-react";
import "../../styles/Dashboard.css";
import profileimg from "../../assets/images/profile.png";

const DashboardHeader = () => {
  const navigate = useNavigate();

  return (
    <header className="main-header">
      <div className="header-content">
        <div className="header-left">
          <div className="brand">
            <Link to="/dashboard">
              <img
                src="https://cdn.builder.io/api/v1/image/assets/TEMP/ff79270abae4069c92179445e6e8601c1e5c3f7c?placeholderIfAbsent=true"
                className="signup-logo"
                alt="Pfizer Logo"
              />
            </Link>
          </div>
          <nav className="main-nav">
            <Link to="/dashboard" className="nav-item active">Dashboards</Link>
            <Link to="/data-sources" className="nav-item">Data Sources</Link>
            <Link to="/users" className="nav-item">Users</Link>
            <Link to="/dashboard" className="nav-item">More</Link>
          </nav>
        </div>
        <div className="header-actions">
          <button className="icon-button">
            <Search size={20} color="#6B7280" />
          </button>
          <button className="icon-button">
            <Bell size={20} color="#6B7280" />
          </button>
          <button className="icon-button">
            <HelpCircle size={20} color="#6B7280" />
          </button>
          <Link to="/profile" className="user-avatar">
            <img
              src={profileimg}
              alt="User Profile"
              className="avatar-img"
            />
          </Link>
        </div>
      </div>
    </header>
  );
};

export default DashboardHeader;



