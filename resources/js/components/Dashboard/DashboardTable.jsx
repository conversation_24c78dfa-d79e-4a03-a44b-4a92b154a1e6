import React from "react";

const tableData = [
  {
    icon: "ti-trending-up",
    name: "Leadership Team Data Board",
    brands: "Brand X, Brand Y, Brand Z",
    userType: "Leadership Team",
  },
  {
    icon: "ti-chart-bar",
    name: "Marketing Team Data Board",
    brands: "BrandX, Brand Y",
    userType: "Marketing Team",
  },
  {
    icon: "ti-clock",
    name: "Media Team Data Board",
    brands: "Brand X",
    userType: "Media Team",
  },
  {
    icon: "ti-users",
    name: "Media Team Data Board",
    brands: "Brand Y",
    userType: "Media Team",
  },
];

const DashboardTable = () => {
  return (
    <div className="dashboard-table-container">
      <div className="table-header">
        <div className="table-title">20 boards, 3 Brands</div>
        <div className="table-actions">
          <button className="filter-btn">
            <i className="ti ti-filter" />
            <span>Filter</span>
          </button>
          <i className="ti ti-list table-view-icon" />
          <i className="ti ti-grid-dots table-view-icon" />
        </div>
      </div>

      <div className="table-wrapper">
        <table className="dashboard-table">
          <thead>
            <tr>
              <th>Board Name</th>
              <th>Available Brands</th>
              <th>User Type</th>
              <th></th>
            </tr>
          </thead>
          <tbody>
            {tableData.map((row, index) => (
              <tr key={index}>
                <td>
                  <div className="board-name">
                    <div className="board-icon-wrapper">
                      <i className={`ti ${row.icon} board-icon`} />
                    </div>
                    <span className="board-title">{row.name}</span>
                  </div>
                </td>
                <td className="board-brands">{row.brands}</td>
                <td className="board-user-type">{row.userType}</td>
                <td>
                  <div className="board-actions">
                    <i className="ti ti-eye action-icon" />
                    <i className="ti ti-dots-vertical action-icon" />
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="table-pagination">
        <div className="pagination-controls">
          <button className="pagination-btn">
            <i className="ti ti-chevron-left" />
          </button>
          <button className="pagination-btn active">1</button>
          <button className="pagination-btn">2</button>
          <span className="pagination-ellipsis">...</span>
          <button className="pagination-btn">10</button>
          <button className="pagination-btn">
            <i className="ti ti-chevron-right" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default DashboardTable;
