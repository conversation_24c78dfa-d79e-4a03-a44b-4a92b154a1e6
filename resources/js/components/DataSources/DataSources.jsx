import React, { useState } from 'react';
import DashboardHeader from '../Dashboard/DashboardHeader';
import { ChevronDown, ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';
import './DataSources.css';

const DataSources = () => {
  const [showNotification, setShowNotification] = useState(true);
  const [currentPage, setCurrentPage] = useState(1);

  const dataSources = [
    {
      id: 1,
      icon: "chart-bar",
      name: "Measurement Benchmarks",
      description: "Internal KPIs and historical performance baselines",
      syncPeriod: "Daily",
      lastSync: "22.05.2025 23:59",
      isActive: true
    },
    {
      id: 2,
      icon: "server",
      name: "Server-Side Web Logs",
      description: "Raw web event data from <PERSON><PERSON>zer's server architecture",
      syncPeriod: "Weekly",
      lastSync: "17.05.2025 23:59",
      isActive: false
    },
    {
      id: 3,
      icon: "snowflake",
      name: "Snowflake",
      description: "<PERSON><PERSON><PERSON>'s central data warehouse containing campaign and spend data",
      syncPeriod: "Weekly",
      lastSync: "12.05.2025 23:59",
      isActive: false
    }
  ];

  return (
    <div className="dashboard">
      <link
        rel="stylesheet"
        href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@latest/dist/tabler-icons.min.css"
      />

      <DashboardHeader />

      <div className="dashboard-content">
        {showNotification && (
          <div className="notification-banner">
            <div className="notification-message">
              <i className="ti ti-bell notification-icon" />
              <span>
                Discover the power of our SmartRX's features for a hands-on
                experience of its seamless functionality
              </span>
            </div>
            <div className="notification-actions">
              <button className="notification-demo-btn">View demo</button>
              <i
                className="ti ti-x notification-close"
                onClick={() => setShowNotification(false)}
              />
            </div>
          </div>
        )}

        <div className="data-sources-header">
          <h1 className="page-title">Data Sources</h1>
          <button className="create-integration-btn">
            <i className="ti ti-plus" />
            <span>Create New Integration</span>
          </button>
        </div>

        <div className="connect-section">
          <div className="connect-icon">
            <i className="ti ti-link" />
          </div>
          <span className="connect-text">Connect</span>
        </div>

        <div className="data-sources-table">
          <table className="sources-table">
            <thead>
              <tr>
                <th></th>
                <th>Description</th>
                <th>Sync Period</th>
                <th>Last Sync</th>
                <th></th>
                <th></th>
              </tr>
            </thead>
            <tbody>
              {dataSources.map((source) => (
                <tr key={source.id}>
                  <td>
                    <div className="source-icon">
                      <i className={`ti ti-${source.icon}`} />
                    </div>
                    <span className="source-name">{source.name}</span>
                  </td>
                  <td className="source-description">{source.description}</td>
                  <td>
                    <div className="sync-period">
                      <span>{source.syncPeriod}</span>
                      <ChevronDown size={16} />
                    </div>
                  </td>
                  <td className="last-sync">{source.lastSync}</td>
                  <td>
                    <div className="settings-icon">
                      <i className="ti ti-settings" />
                    </div>
                  </td>
                  <td>
                    <div className="toggle-switch">
                      <input
                        type="checkbox"
                        id={`switch-${source.id}`}
                        checked={source.isActive}
                        readOnly
                      />
                      <label htmlFor={`switch-${source.id}`}></label>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="data-security-info">
          <p>
            All integrations follow Pfizer's enterprise security and compliance standards (GDPR, HIPAA, CCPA).
            Data is encrypted in transit and at rest. Access is managed via Pfizer Single Sign-On (SSO) and role-based permissions.
          </p>
        </div>

        <div className="retention-policy">
          <div className="retention-label">Retention Policy (Applicable for all)</div>
          <div className="retention-selector">
            <select className="retention-select">
              <option value="3">3 years</option>
              <option value="5">5 years</option>
              <option value="7">7 years</option>
            </select>
            <button className="save-btn">SAVE</button>
          </div>
        </div>

        <div className="pagination">
          <button className="pagination-btn">
            <ChevronLeft size={16} />
          </button>
          <button className={`page-number ${currentPage === 1 ? 'active' : ''}`}>1</button>
          <button className={`page-number ${currentPage === 2 ? 'active' : ''}`}>2</button>
          <div className="pagination-dots">...</div>
          <button className={`page-number ${currentPage === 10 ? 'active' : ''}`}>10</button>
          <button className="pagination-btn">
            <ChevronRight size={16} />
          </button>
        </div>
      </div>
    </div>
  );
};

export default DataSources;
