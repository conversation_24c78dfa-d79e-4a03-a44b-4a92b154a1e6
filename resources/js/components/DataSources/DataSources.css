/* Data Sources Page Styles */

.data-sources-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
}

.page-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin: 0;
}

.create-integration-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background-color: #000067;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
}

.create-integration-btn i {
  font-size: 1rem;
}

/* Connect Section */
.connect-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.connect-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: #f3f4f6;
  border-radius: 0.375rem;
}

.connect-icon i {
  color: #000067;
  font-size: 1.25rem;
}

.connect-text {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
}

/* Data Sources Table */
.data-sources-table {
  margin-bottom: 2rem;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.sources-table {
  width: 100%;
  border-collapse: collapse;
  background-color: white;
}

.sources-table th {
  text-align: left;
  padding: 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  border-bottom: 1px solid #e5e7eb;
}

.sources-table td {
  padding: 1rem;
  font-size: 0.875rem;
  color: #111827;
  border-bottom: 1px solid #e5e7eb;
  text-align: left;
}

.source-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: #f3f4f6;
  border-radius: 0.375rem;
  margin-right: 0.75rem;
}

.source-icon i {
  color: #000067;
  font-size: 1.25rem;
}

.source-name {
  font-weight: 500;
}

.source-description {
  color: #6b7280;
  max-width: 20rem;
}

.sync-period {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
  width: fit-content;
  cursor: pointer;
}

.last-sync {
  color: #6b7280;
}

.settings-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  cursor: pointer;
}

.settings-icon:hover {
  background-color: #f3f4f6;
}

.settings-icon i {
  color: #6b7280;
  font-size: 1.25rem;
}

/* Toggle Switch */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 3rem;
  height: 1.5rem;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-switch label {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e5e7eb;
  transition: .4s;
  border-radius: 1.5rem;
}

.toggle-switch label:before {
  position: absolute;
  content: "";
  height: 1.25rem;
  width: 1.25rem;
  left: 0.125rem;
  bottom: 0.125rem;
  background-color: white;
  transition: .4s;
  border-radius: 50%;
}

.toggle-switch input:checked + label {
  background-color: #000067;
}

.toggle-switch input:checked + label:before {
  transform: translateX(1.5rem);
}

/* Data Security Info */
.data-security-info {
  margin-bottom: 2rem;
}

.data-security-info p {
  font-size: 0.875rem;
  color: #6b7280;
  line-height: 1.5;
  text-align: left;
}

/* Retention Policy */
.retention-policy {
  display: block;
  align-items: center;
  text-align: left;
  gap: 1rem;
  margin-bottom: 2rem;
}

.retention-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #111827;
  font-weight: bold;
  margin-bottom: 15px;
}

.retention-selector {
  display: flex;
  gap: 0.75rem;
}

.retention-select {
  padding: 0.5rem;
  border: 1px solid #e5e7eb;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: #111827;
  background-color: white;
  min-width: 8rem;
  width: 200px;
}

.save-btn {
  padding: 0.5rem 1rem;
  background-color: #000067;
  color: white;
  border: none;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 600;
  cursor: pointer;
}

/* Pagination */
.pagination {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 0.25rem;
}

.pagination-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.25rem;
  cursor: pointer;
}

.pagination-btn:hover {
  background-color: #f3f4f6;
}

.page-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: #6b7280;
  cursor: pointer;
}

.page-number.active {
  background-color: #000067;
  color: white;
  border-color: #000067;
}

.pagination-dots {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  font-size: 0.875rem;
  color: #6b7280;
}

/* Notification Banner */
.notification-banner {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: #eef2ff;
  border-radius: 0.5rem;
  padding: 0.75rem 1rem;
  margin-bottom: 1.5rem;
}

.notification-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.notification-icon {
  color: #000067;
  font-size: 1.25rem;
}

.notification-message span {
  font-size: 0.875rem;
  color: #111827;
}

.notification-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.notification-demo-btn {
  color: #000067;
  font-size: 0.875rem;
  font-weight: 500;
  background: none;
  border: none;
  cursor: pointer;
}

.notification-close {
  color: #6b7280;
  font-size: 1.25rem;
  cursor: pointer;
}