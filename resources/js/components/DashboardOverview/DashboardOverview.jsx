import React, { useState, useRef } from 'react';
import './DashboardOverview.css';
import { TrendingUp, TrendingDown, Calendar, Filter, Share, MoreHorizontal } from 'lucide-react';
import { <PERSON><PERSON>hart, Line, Bar<PERSON>hart, Bar, XAxis, YAxis, ResponsiveContainer, Legend, PieChart, Pie, Cell, Tooltip } from 'recharts';
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "../ui/table";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "../ui/tabs";
import { Button } from "../ui/button";
import { Card, CardHeader, CardTitle, CardContent } from "../ui/card";
import "./UserDemographics.css";
import DashboardHeader from '../Dashboard/DashboardHeader';

// Age data for bar chart
const ageData = [
    { age: '15-20', value: 350 },
    { age: '21-25', value: 450 },
    { age: '26-30', value: 320 },
    { age: '31-35', value: 280 },
    { age: '36-40', value: 420 },
    { age: '41-45', value: 110 },
    { age: '46-50', value: 480 },
];

// Gender data for pie chart
const genderData = [
    { name: 'Male', value: 750 },
    { name: 'Female', value: 1740 },
];

// Geography data for bar chart
const geographyData = [
    { language: 'English', value: 2600 },
    { language: 'French', value: 800 },
    { language: 'Spanish', value: 1900 },
    { language: 'Arabic', value: 2000 },
    { language: 'Mandarin', value: 1950 },
    { language: 'Russian', value: 1700 },
    { language: 'Others', value: 1900 },
];

// Location data for table and bar chart
const locationData = [
    { country: 'United States', users: 577, active: 360, coords: { lat: 37.0902, lng: -95.7129 } },
    { country: 'Italy', users: 554, active: 310, coords: { lat: 41.8719, lng: 12.5674 } },
    { country: 'Canada', users: 537, active: 290, coords: { lat: 56.1304, lng: -106.3468 } },
    { country: 'Brazil', users: 521, active: 340, coords: { lat: -14.2350, lng: -51.9253 } },
    { country: 'Argentina', users: 488, active: 250, coords: { lat: -38.4161, lng: -63.6167 } },
    { country: 'Mexico', users: 452, active: 320, coords: { lat: 23.6345, lng: -102.5528 } },
    { country: 'United Kingdom', users: 406, active: 290, coords: { lat: 55.3781, lng: -3.4360 } },
    { country: 'France', users: 399, active: 350, coords: { lat: 46.2276, lng: 2.2137 } },
    { country: 'Germany', users: 368, active: 320, coords: { lat: 51.1657, lng: 10.4515 } },
    { country: 'Others', users: 320, active: 190, coords: { lat: 0, lng: 0 } },
];


const worldMapData = {
    viewBox: "0 0 1010 510",
    countries: [
        { id: "USA", d: "M230.3,163.5l-0.9,2.6l-3.8,0.3l-3.5,0.8l-3.5,1.6l-2.5-0.5l-1.5-1.5l-2.8,0.6l-1.2,4.2l-1.8,2.1l-1.8-0.8l-2.3,0.3l-2.7,1.2l-1.3,1.5l-4.1,1.7l-3.2,0.6l-0.3,0.9l2.4,1.2l1.5,3.8l0.3,3.6l-1.9,0.7l0.7,2.1l-0.5,1.7l-2.5-0.1l-2.9-0.5l-0.6,0.9l-3.4-0.5l-0.8,1.3l-2.1-0.2l-1.6,0.6l-2.7-2.2l-3,0.2l-0.4,0.9l0.8,1.8l-1.2,0.7l-0.9-0.8l-0.5,0.2l-4.5-0.1l-2.8,0.8l-3.5,0.8l-2.8,1.8l-3.3,1.2l-0.3,1l3.5,2.4l-0.2,2l-1.5,2l-0.3,1.3l1.8,2.2l-0.6,2.1l1.3,4.1l-0.8,1.1l0.8,0.7l-0.5,1l-2.8-0.1l-4.2-1.5l-1.2,0.7l-3.6-0.1l-4,1.3l-1.1,1.3l-0.4,2l-2,1.2l-3.8,1.3l-0.6,0.7l-4.6-1.3l-0.6,0.7l-0.1,0.9l-3.3,1.9l-1.7-0.1l-0.2-2.8l0.7-2.9l-1.7-0.6l-0.6,0.9l-2.3-0.1l-1.1,1.1l-2.2,0.6l-1.4,0.9l-2.3,0.5l-1.8,1l-5.3,0.7l-3.9,0.1l-3.7,1.5l-0.4,1.7l-2.9,1.2l-1,0.1l-1.7,1.2l-0.9,1.7l0.5,0.8l-0.7,0.6l-0.1,1.8l-3.9,0.2l-1-0.4l-1.9,1.8l-1.1-0.6l-0.6,1.1l-1.2-0.7l-0.6,0.5l-2.2-0.3l-1,1.8l-0.7-0.7l-1.2,0.8l-0.7-1.4l-1.2-0.2l-0.5-1l-1.4,0.7l-1.1-1.4l-3.3,0.3l-0.9-0.3l-0.9,1.2l-1.5-1l-0.7,0.1l-0.2,0.8l-2.5,0l-0.1-4.8l-0.5-0.3l-2.4,1l-1.8,1.3l-2.6,0.4l-0.5,0.9l0.2,2.2l-1.8,0.9l-0.2,1.1l-2.4,0.5l-1.1,1.7l-1.3-0.8l-2.2,0.8l-0.9-0.1l-0.3,0.9l-1.6,0l-2.2,1.3l-1.4-0.7l-0.4,2.5l-0.7,0.3l-0.1,1.1l-1.7,1.2l-0.7,1.1l0.5,0.7l-0.5,0.9l-2.7,1.2l-0.4,1l-3.5,1.2l-0.3-0.5l-1.4,0.6l-0.4,1l-0.6-1.3l-1.4,0.4l-0.3-0.7l-1.8-0.4l-1.3,0.1l-1.5,0.6l-0.2,1.1l-1.9,0.8l-0.2,0.9l-1.9,0.3l-0.1,0.7l-2.1,0.6l-0.5,1.8l-0.5,0.3l-0.1,1.5l-1.4,1l0.3,1.4l-0.5,0.9l-0.8,0l-0.4,0.7l-1.6,0.1l-0.8,0.8l-1.8-0.4l-1,0.2l-0.4-3.1l-0.7-0.1l-0.3,0.7l-2.9,1.3l-3.7-0.6l-0.6-0.7l-1.3,0.7l-1.8-0.4l-2.2,1l-3.2-1l-0.4,0.6l-3.2,0.5l-1.4,1l-1.5-0.4l-1.8-1.1l-1.2,0.8l-2.5-2.2l-1.2-0.2l0.1-4.7l-2.7-2l-0.8,0.7l-0.1,1.3l-3.3,1.5l-0.6-0.5l-0.7,0.7l-2.7-0.8l-1.3,0.5l-0.7-0.8l-2.7,0.7l-0.6-0.3l-1.6,1.1l-1-0.2l-1-1.2l-1.6-0.2l-1.1-0.9l-1.2,0.5l-0.3-0.8l-2.9-0.3l-0.5-0.9l-1.8-0.8l-0.8,0.2l-1.7-0.7l-0.2-1.1l-1.7-0.3l-0.9,0.2l-0.2-0.7l-2.1,0.2l-2.2-2.7l-1.2-0.7l0.1-2l1.1-0.5l-0.1-0.8l-1.4-0.7l-0.9,0.2l-0.6-1.2l-1.2-0.4l-1,0.3l-0.9-0.4l-2.8,1.1l-1-0.2l-0.4,0.8l-1.5,0.1l-1.4,2.2l-0.7-0.5l-1.5,0.6l-0.6-0.3l-0.9,0.7l-1.1-0.5l-1.8-1.7l-0.7,0.4l-2.2-0.8l-0.9,0.1l-1.5-0.9l-1.2,0.8l-0.6-0.8l-1.5-0.2l-2.1-1l-2.6,0.7l-1-0.8l-0.9,0.2l-1.1-0.1l-1.4,0.6l-0.6-0.9l-1.2-0.1l-1.5,0.5l-0.9-0.1l-2,1.3l-1.1,0.1l-0.3-0.7l-1.7-0.3l-1.1,0.9l-1.4-0.3l-2.7,1l-1.2-0.8l-1.6,0.3l-1.7-0.8l-1,0.3l-2.9-0.3l-1.8,0.5l-4.1-0.8l-1.7-1.1l-3,0.3l-2.3-1.3l-1.1-1.3l-3.5,0.3l-1.9-1.5l-2.7,1.2l-0.8-0.1l-3.9,1.9l-2.7,0.4l-3.4,2.3l-2.3,0.7l-0.1,0.7l-2.8,0.4l-1.7-0.5l-0.7,1.2l-1.7,0.6l-0.9-0.1l-1.3,0.7l-0.2,0.8l-1.5,1.5l-0.3,1.1l-1.8-0.2l-1.6,0.4l-3.7-0.2l-1.9-1.3l-1.5,0.3l-0.2,1.1l-1.2,0.1l-2.6,1.7l-2.2-0.9l-1.7,0.2l-0.3,0.7l-2.2,0.5l-0.7,1.5l-1.2,0l0,0l-0.9-0.4l0,0l-0.7,0.8l-1-0.2l-0.6,0.9l-1.8-0.1l-2.2,1l-1.5-1l-1.1-0.1l-1,0.5l-0.4-1.5l-1.5-0.1l-2-0.7l-0.7,0.5l-1.5-0.2l0.2-1.3l-0.7-0.7l-0.7,0.5l-0.8-0.7l-1.3,0.7l-0.7-1.1l-2.2-0.1l-0.8-0.3l-0.1-1.3l-0.9-0.5l0.2-0.7l-1.3-0.3l-0.7,0.3l-0.5-1l-0.8-0.1l-0.5-0.8l-1.3-0.3l0.3-0.9l-0.9-0.9l0.5-1l-0.9-1.4l0,0l1.4-0.5l-0.1-1.1l0,0l-0.2-1.8l-1.1-0.8l0.1-0.7l-1-0.9l-0.9,0l0,0l-0.7,0.3l-0.8-0.3l-1.1-1.2l-0.4-2.1l-1.2-0.7l-1.5,0.8l-1-0.2l-0.3-1.6l-1-1L66,155l-0.9-0.2l-0.5-0.8l-1.4-0.3l-0.5-1.7l-0.5-0.4l-1.8,0.2l-1.1-1.1l-0.5-1.5l-1-0.5l0.1-0.8l-1.3-1l-0.2-1.4l0.2-0.8l-0.9-0.3l-0.3-1.3l-0.8-0.7l-0.4-2l0.9-1l-0.8-0.9l-0.3-1.9l0.8-0.6l-0.2-0.8l0.7-0.7l-0.5-1l0.3-1.4l0.5-0.3l0.1-1.2l1.4-0.9l0.6-1.4l2.1-0.2l0.8-0.9l-0.2-0.8l-1.3-0.3l-0.3-0.9l0.5-1.3l-0.3-2.2l-1.2-1.4l0.5-2.1l-0.2-1l0.8-0.6l-0.1-0.8l-0.7-0.4l0.6-1l-0.7-1.1l0.3-0.6l-1-1.9l-0.6-0.3l0.1-1.2l-1.8-1.5l-0.9,0.2l-0.1,0.7l-1.2,0.5l-0.3-0.9l-1.8-0.3l-1.4,0.3l-0.8-0.9l-2.2-0.3l-1-0.9l-1-0.1l0.2-1.1l-0.7-0.5l-1.1,0.2l-0.4-0.7l-1.4-0.3l-1.3-0.9l0,0l0.6-0.6l0.9,0.3l0.8-1.1l1.1-0.3l0.9-1l0.9,0.3l0.5-0.5l2.1-0.1l0.3-0.5l2.5-1.1l0.4-0.5l1.3,0.4l-0.1-1.4l1-0.3l0.1-0.8l2.6-0.4l0.9-0.9l0.5,0.1l0.5-0.6l1.9-0.2l0.9-0.4l0.3-0.7l2.7-0.5l0.8,0.7l1.7-0.4l1.1,0.5l1.6-0.5l1.1-1.8l1.5-0.3l1.5,1.4l1.3,0.3l-0.2,1.9l0.9,0.2l2.9-0.7l0.6-0.5l1-0.1l0.8-1.3l0.2-2l0.9-0.7l0.7-2.9l2.8-2.8l0.9-0.2l1.5-1.3l1.5-0.3l1.5,0l1-0.9l1.5-0.2l1.1,0.3l1.1-0.3l0.7-0.7l1.8,0.2l0.9-0.3l1.8-1.8l0.3-1.5l1.7-1.1l1.9-1.9l0.9,0.4l0.1,1.1l1.3-0.1l1.2,0.7l-0.1,1.7l0.7,0.9l-0.9,0.3l-0.5,0.8l-0.8,0.1l0.1,1.1l1.3,0.8l0.2,0.9l0.6,0.5l1-0.3l0.9,0.5l0,0.9l0.9,0.8l0.6-0.3l0.9,0.9l-0.2,0.8l1.1,0.6l-0.3,0.7l0.8,0.2l-0.1,0.8l0.7,0.2l1.2-1l0.6,0.5l0.2,0.9l0.9,0.5l0.9-0.3l0.5,0.7l1.1-1.2l1.6-0.6l0.9-0.9l0.6,0.2l0.8-0.5l-0.1-0.8l0.8,0l0.8,0.7l0.8-0.7l-0.2-1.3l0.4-1.1l1.3,0.3l0.8-1.3l1.1,0.1l0.1,0.7l1.1-0.1l0.7,0.5l0.1,1l0.7,0.3l0.1,0.9l0.8,0.2l0.8-0.6l0.2-1l0.9-0.6l0.3,1.4l0.5,0.2l0.8-0.8l0.5,0.3l1.2-0.6l0.2-0.4l1.8-0.2l0.5-0.3l1.4,0.1l0.5,0.8l0.4-0.5l0.7,0.7l0.6-0.2l0.8,0.4l1.7-0.4l0.4,0.3l1.2-0.5l0.1-0.9l-0.7-0.9l0.9-0.4l0.4,0.8l0.7-0.3l0.3-0.8l-0.7-0.8l0.7-0.5l0-0.7l1.5-0.3l1.4,0.2l1.1-0.6l0.9,0.1l0.8-0.5l1.4,0.8l0.9-0.1l1.7-1l0.8,0.7l0.1,0.7l0.6-0.3l1.3,0.9l0.3,1.1l0.8,0.1l0.5-0.9l0.8,0l0.5-0.4l1.9,0.3l0.7-0.5l0.7,0.3l0.5-0.3l0.8,0.3l0.9-0.2l0.9,0.5l0.9,0l0.5-0.6l1-0.1l0.5-0.9l1.1-0.3l1.3,0.2l0.2,1l0.9,0.1l1.2-0.9l1.3-0.2l0.4-0.8l1.8-0.4l0.5,0.3l2.2-0.7l0.6,0.3l0.1,0.7l0.8,0.2l1.6-1.1l2.5-0.5l0.7,0.4l0.2,0.9l1.2,0l1.3-0.2h0l0.9-0.6l1.1,0.1l0.5-0.5l0.7,0.2l1.4-0.1l0.5,0.4l1.7-0.9h0.4l1.5,1.1l0.8,0l0.4-0.4l0.9,0.9l1-0.2l0.8-1.8l0.8-0.3l0.9-1.3l2.9,0.1l0.2-1l1.1,0.5l0.3,0.7l1.6,0.6l0.8-0.7l0.4,0.9l1.1-0.1l0.8-1.3l1.9-0.2l0.4-0.8l0.5,0.3l1.3-0.7l0.3-1l1.9,0.5l0.5-0.5l0.5,0.4l0.7-0.7l1.5,0.8l1.3-0.5l0.2-0.9h1l0.5,1.1l1.3-0.3l0.4,0.6l1-0.3l0.2-0.8l0.7,0.7l1.8,0l1.5-0.6l0.8,0.6l1.8-1l0.8,0.3l0.3-0.9l1.6-0.2l0.5,0.5l1.9-1.2l0.9,0.4l-0.3,1l0.7,0.6l0.3-1l2.7-0.7l0.9,0.4l0.3-0.8l1.1-0.3l1.2,0.5l0.4-0.5l0.7,0.6l0.5-0.6l1.4,0l0.6,0.6l1.2-0.7l1.2,0.5l1-0.5l1,0.1l0.4,0.6l2.1,0.3l0.5-0.7l1.8,0.1l0.5-0.5l1.2,0.5l1.4-1.2l1,0.3l1.2-0.1l-0.1,1l1-0.3l0.4,1.3h1l0.2-0.8l0.8,0.7l0.9,0l0.4,0.7l1.2-0.2l0.3-0.5l1.9,0.2l0.4-0.8l0.9,0.5l0.8-0.5l1.7,0.5l0.3,0.8l1.8-0.4l0.6,0.4l0.8-0.9l0.3,0.7l1.7-0.1l0.5,0.5l1.3-0.1l0.8,0.4l0.8-0.8l0.6,0.4l1.6,0.1l0.7-0.6l1.3,0.8l0.6-0.6l1.1-0.1l0.4,0.4l2.2,0.2l0.6-1.1l0.8,0.7l0.6-0.6l0.8,0.3l0.6-0.3l0.6,0.5l1.1-0.1l2.2,0.6l0.9-0.8l0.7,0.3l1.2-1l0.5,0.3l2.2-0.5l1.3,1.1l0.8-0.5l0.4,0.5l1.9-0.8l0.4,0.9l0.8-0.1l0.5-1l1,0.4l0.2-0.6l1.5,0l0.1-0.6l0.9,0l-0.1,0.9l1.2-0.6l0.5,0.3l1.6-0.6l0.2,0.6l0.7-0.7l0.6,0.4l1-0.4l1.2,0.2l1.2-0.9l-0.1-0.8l2.3-0.1l1.3,0.4l1.7-0.4l0.9,0.4l0.5-0.3h0l2.4-0.1l0.6,0.3l1.3-0.8l0.6,0.7l1.6-0.4l2.3,0.8l0.7-0.4l1.5,0.7l0.7,0l0.6-1.1h1.1l0.4-1.3l0.8-0.3l-0.3-0.7l0.7-0.3l0.2-0.9l0.7-0.2h0l1,0.4h0l0.7-0.8l1.2,0.3l0.3-0.9l1.9-0.1l0.3-0.4l1,0.5l0.9-0.8l0.6,0.3l0.7-0.4l0.3,0.5l2.1-0.4l0.6,0.3l0.8-0.7l1.8,0.2l0.2-0.5L230.3,163.5z", fill: "#D0D0D0" },
        { id: "CAN", d: "M191.5,102.2l0.4-1.1l-0.8-0.9l0.2-0.6l-0.3-0.5l0.3-1.5l-0.3-0.9l-0.6,0.4l-0.4-0.3l0.2-2.3l-0.1-0.9l-0.5,0.3l-0.8-0.1l-1.7-0.9l-0.9,0.2l-0.9-1.2h-0.7l-0.8-1.1l-0.3-1l0.3-0.3l-0.2-3l-0.3-0.7l-0.9-0.4l-0.5-1.1l-0.9,0.1l-0.2,0.9l-0.3,0.2l-0.3-0.8l-0.7-0.1l-0.2-0.3l0.2-0.7l-0.3-0.7l0.3-0.9l-0.8-1.1l0.1-2l0.6-0.3l-0.1-0.8l0.7-0.2l-0.1-1l-0.4-0.8l0.2-0.5l-0.5-1.3l-0.5-0.2l0.2-1.3l-0.1-1.1l-0.4-0.1l-0.3-1l-0.6,0.1l-0.4,0.8l-0.3,2.5l-0.4-0.1l0.2-0.8l-0.3-0.9l-0.6-0.5l0.6-0.2l0.1-0.8l-0.4-0.4l-0.7,0.3l-0.2,0.8l-1,0.3l-0.8,0.9l-0.1-0.8l-0.5-0.4l-0.1-0.6l-1.3,0.9l-0.9,0.2l-0.3,1.6l0.2,0.6l-1.4,1.9l-0.6,0.2l0-1.1l-0.8-0.2l-0.7,0.7l-0.4-0.3l-0.6,0.4l-0.7-0.7l-0.1-0.8l0.6-0.4l-0.4-0.2l-0.2-0.9l-0.5,0.3l-0.8-0.3l-0.2,0.9l-0.6-0.2l-0.6,0.4l-0.1,0.7l-0.6-0.2l0.1-0.6l-0.3-0.2l-0.3,0.6l-0.4-0.1l-0.4-0.7l0.1-0.5l-0.3-0.3l-0.7-0.1l-0.3-0.6l-0.6-0.1l-0.2,0.3l-0.5-0.1l-0.2-0.5l-0.5,0.1l-0.2-0.7l-0.4-0.1l-0.5,0.4l-0.7-0.6l-0.3,0.1l-0.2-0.5l-0.9-0.3l0.1-0.5l-0.3-0.3l-0.5,0.2l-0.8-0.4l0.4-0.9l-0.2-0.9l-0.6-0.4l-0.3,0.7l-0.9-0.3l-0.2-0.8l-0.6,0.1l-0.5-0.8h-0.7l-0.8-0.4L149,57l-0.6-0.6l-1,0.2l-0.5-0.3l-0.8,0.3l-0.3,0.8l-0.5,0.3l0.1,0.5l-0.5,0.4l-0.7-0.1l-0.7-0.7l-0.5,0.1l-0.5-0.8l-1-0.5L141,56l0.2-0.5l-0.4-0.5l-0.6,0.1l-0.4-0.4l-1-0.1l-0.6-1.3l-0.7-0.6l-0.8,0.3l-2.3-0.8l-1-0.1l-0.5,0.8l-0.9,0.3l-0.6-0.7l-1.5-0.5l-1.4-0.1l-0.5-0.8l-1.5-0.2l-0.5,0.5l-0.9,0.2l-1-0.5l-0.9-0.2l-2.1,0.2l-0.5,0.5l-1-0.5l0,0l-0.4-0.9l-1.4-0.9l-0.5,0.1l-1.6-0.5l-0.8-1.2l-0.7-0.3l-1,0.7l-0.2,0.6l-2.5,0.7l-0.5-0.2l-0.2-0.8l-1.4-0.5l-0.9,0.3l-0.4,1l-0.5,0.2l-0.2,1.3l-0.7-0.2l-0.8,0.7l-0.3-0.2l-0.5-1.9l-1-0.3l-1.2,0.8l-1.1-1.2l-1.4,0.3l-1-0.2l-0.4,0.7l-0.6,0.1l-1.3-0.8l-0.3,0.5l-0.8,0.2l-2.4-0.3l-0.8,0.3l-0.3-0.3l-1,0.5l-0.4-0.7l-0.5-0.1l-0.1,0.6l-0.6,0.1l-0.1,0.5l-1.5,0.5l-0.1,0.6l-1.2,0.6l-0.4-0.8l-0.9-0.2l-0.9,0.3l-0.8-0.7l-1.7-0.5l-0.4,0.5l-0.9,0.1l-1.2-0.8l-1.2-0.1l-0.9-0.5l-1-0.1l-0.5-0.5l-0.8,0.2l-0.5,0.5l-0.7-0.1l-0.1,0.6l-1.7-1.1l-0.9,0.2l-0.7-1l-0.6-0.1l-0.8,0.3l-0.5-0.9l-1.2-0.7l-0.2-1L63,46.8h-0.5l-0.2,0.5l-0.6-0.1l-1.3,0.3l-0.6,0.6l-1.3-0.1l-0.6-0.6l-0.6,0.2l-0.4-0.3l-0.7,0.4l-0.8-0.1l-0.8,0.9l-0.8-0.2l-1.1,0.4l-0.8-0.4l-1.2,0.2l-0.3-0.5l-0.7,0.1l-0.2-0.3l-0.9,0.7l-0.9-0.1l-0.2-0.5l-0.6-0.1l-0.4-0.8l-0.6,0l-0.1-0.5l-0.5-0.3l-0.6,0.2l-0.1-0.8l-0.9-0.1l-1.8,0.3l-0.7-0.3l-0.7,0.6L38.5,46l-0.9-0.1l-0.7-0.4l-1.2,0.9l0,0l-1.6,0.3l-0.3,0.5l0.7,2l-0.1,0.8l1.1,0.9l-0.7,1.6l0.4,0.8L35,54l0.8,0.4l0.2,1.1l0.9,0.8l-0.2,1l-1.3,1.7l-0.1,1.3l0.7,0.9l-0.3,1l-0.6-0.2l-0.3,0.7l-0.8,0l-0.2,0.8l0.7,0.9l-0.2,0.9l0.6,0.2l-0.1,0.7l0.7,0.2l-0.3,1.2l0.2,0.5l-0.5,0.3l-0.1,0.7l-0.6,0.3l0.4,0.5l-0.6,0.8l0.2,0.5l-0.5,0.6l0,0l0,0l0,0.5l-0.5-0.1l-0.8,0.6l0.1,0.6l-0.4,0.6l0.4,0.5l-0.6,0.4l-0.1,0.8l0.4,0.3l-0.3,0.5l0.3,0.6l-1.2,0.2l0.1,1.6l-0.3,1.1l0.8,1.4l-0.5,0.7l1.3,1.7l0.9,0.6l0.9,0.2l1.4,1l1.3,0.6l0.6,2.5l-0.4,0.6l0.2,0.6l2.1,1l0.2,1.5l0.8,0.9l0.3,1.5l1.3,1.7l-0.4,0.7l1.4,0.9l0,1.7l1.3,0.7l-0.2,0.7l1.9,1l0.3,0.6l1.1,0.5l0.5,1l0.1,1.2l0.8,0.6l0.4,1.2l0.8,0.4l0.3,0.6l1.1,0.1l0.1,0.4l1.7,0.5l0.2,0.4l1.1,0.4l0.4,0.5l1.5,0.3l1.1,1.3l0.7,0.3l1.5,2.2l1.1,0.4l0.5,0.7l0.6-0.3l0.9-1.5l0.4,0.1l0.5-0.5l0.3,0.1l0.6-0.7l-0.1-0.4l0.9-0.3l1.7,0l-0.2-1l0.5-0.2l0.5,0.3l0.5-0.2l0.5,0.5l0.5-0.4l0.4,0.3l0.3-0.2l0.3,0.5l1.2,0.2l0.4,0.5l0.5-0.2l0.5,0.3l0,0.5l0.8-0.2l0.3,0.2l1.1-0.3l0.5,0.5l0.7-0.1l0.1-1l0.7-0.1l-0.8-2.2l1.7-2.5l0.7-1.9l0.6-0.7l-0.3-0.9l0.7-1.8l-0.5-1.3l0.3-2.2l-0.2-0.6l0.6-0.9l0-1l0.7-2.7l0.6-1l-0.3-0.8l0.3-0.5l-0.7-0.7l0.4-1.1l-0.3-0.9l0.4-0.8l-0.1-0.6l1.3-1l0.2-1l0.8-0.3l0.5-1.3l0.6,0l1-0.7l-0.2-0.7l0.4-0.3l-0.3-0.9L92,82l0.1-0.8l0.9,0.3l0.3-0.7l-0.6-0.7l0.7-1.1l-0.5-0.6l0.2-0.4l-1.3-0.3l0.4-1.1l-1.2-0.7l0.2-1.3l1.5,0l-0.3-0.8l1.9,0.4l1.1-1.1l0.5,0.3l0.6-0.4l0.5,0.2l0.3-0.6l0.9-0.3l0.7,0.8l0.9,0l0.3,0.3l0.7-0.3l1.3,0.7l0.5,0l0.5,0.4l1.4-0.3l1.1,0.6l0.4-0.2l1.7,0.7l0.1-1l1.1,0.6l0.5-0.8l0.5,0l0.2-1.1h0.8l1.1-0.8l0.6,0.7l2.1-0.7l0.5-1l0.8,0.2l0.5-0.4l0.2,0.6l0.7,0.2l0.5-0.3l0.8,0.2l0.8-0.7l1.3-0.2l0.1-0.5l0,0l1,0.1l1.2-0.3l0.7,0.2l0.4-0.6l1.6-0.1l0.2-0.8l0.6,0.1l0.2-0.9l0.7,0.2l0.8-0.9h0.5l0.2-0.6l1.4-0.3l0.8-0.6l2-0.2l0.7,0.2l1.4-1.1l0.3,0.5l0.7-0.1l0.4-0.9l1.6,0.1l0.2,0.4l0.9-0.1v-1.3l0.5-0.6l-0.3-0.9l0.7-0.3l0.2-1.3l0.5,0l0.4-0.7l0.6,0.5l0.9-0.2l0.6-0.6l0.4,0.4h0.7l0.5,0.5l0.9-0.8l1,0.2l0.2-0.8l0.6-0.1l0.8,0.7l1.2-0.7l0.4,0.2l1.1-0.4h0.4l0.1,0.5l0.9,0.1l0.2-0.5l0.7-0.1l0.2-1.5l1.3-0.1l-0.2-0.8l0.4-0.4l-0.1-0.6l1.2-0.5v-0.5l0.9,0l1.1-1l1.2,0.7l0.2-0.7l1.8-0.2l0.7-1.1l1-0.1l0.5-0.8l0.7-0.1l0.4-0.6h0.6l0.2-0.8l2-0.4l0.2-0.5l1,0.2l0.8-1l0.7,0.1l-0.6-1l1.4,0l-0.1-0.6l0.8-0.4l1.2,0.7l0.4-0.5l0.5,0.2l0.5-0.5l0.4,0.2l-0.4,0.5l0.6,0.3l-0.1,0.4h0.7l0.7-0.8l0.4,0.5v0.6l1.4,0l-0.2,0.5l0.6,0.2l-0.1,0.3l-1,0.2l-0.1,0.3l1.2,0.4l-0.2,0.6l0.3,0.1l1.7-0.7l0.5,0.2l0.5-1.1l0.7-0.4l0.1,0.8l0.4,0l0.1,0.6l1.1-0.3l-0.4-1l0.9-0.1l-0.1,1l0.9,0.1l0.1-0.3l0.9,0.2l-0.3,0.6l0.5,0.1l0.1-0.7l0.6-0.1l-0.3,0.7l1.2-0.2l0.2,0.3l0.5-0.2l0.4,0.2l0.4-0.5l0.6,0.3l0.4-0.5l0.9,0.3l0,0.5l0.7-0.3l0.3,0.4l0.5-0.2l0.1,0.5h0.5l0.3-0.4l0.8,0.1l0.2-0.4l0.6,0l0,0.5l0.9-0.1l0.2-0.4L191.5,102.2z", fill: "#D0D0D0" },
        { id: "MEX", d: "M184.4,213.9l-1.1-1.7l-1.5-0.9l-1.1,0l-2-1l-1.9-1.8l-2.5-1.8l-1.3-1.3l-0.6-1.9l-0.8,0l-2.1-1.5l-1.2-0.1l-3-1.7l-2.6-2l-0.3-1.3l-1.7-1.6l-1.7-1l-1.9-2.5l-2,0l-2.5-0.1l-2.8-0.8l-1.1,0.1l-3-0.9l-1.2-1.3l-0.6-1.9l-1.2-1.4l-3.7-1.6l-1.5-1.6l-0.7-1.7l-1.3-0.6l-4.4,0.5l-2.4-0.7l-2,0.8l-0.7-0.9l-0.6-1.7l-1-0.8l-2.9-0.4l-1-0.9l-2-0.8l-2.5,0.8l-0.9-0.8l-0.4-0.4l0,0.3l-2.4-1.3l-1.3-0.2l-3.9-1.9l-1.6-1.2l-0.9,0.8l-0.1-0.7l-1.8-1l-1.3-1.5l-0.6-1.6l0.3-1.4l-1.5-2.1l-0.5-1.6l-0.4-3.3l-1.7-3.1l-1.9-2.2l-2.5-2.1l-1.4-0.8l-0.4-1.9l-1-1.3l0.4-1.2l0.5-1.5l-0.6-1.3l-1.2-1l-3.2-0.7l-3-0.9l-2.1-1.1l-0.9-1.8l-1.6-0.6l-0.9-1l-1.8-0.5l-1.5-0.9l-0.4-0.6l-1.4-0.6l-0.5-0.8l-1.8-0.9l-1.4-1.2l-1-1.2l-0.5-1.5l-0.8-1.1l-1.9-1.2l-0.5-1.4l-0.7-0.5l0,0l1.1-3.3l0-3l1.3-2.3l2.4-1.7l2-1.9l3-1.9l2.3-1.1l0.6-0.8l1.5,0l0.2,0.5l-0.5,1.2l0.4,1l-0.1,1.5l-0.7,2.3l0.4,0.5l1.2,0.2l2.6,1l1.8,1l2.4,1.7l0.6,0.2l1.6,0l-0.3,0.8l0.9,1.1l0.2,1l1.1,0.4l0.5,0.6l0.6,0.2l0.8,0.7l0.7,0.1l0.5,1.4l0.7,0.9l0.2,0.8l-1.1,0.1l-0.5,0.7l0,0.5l1.8,0.5l1.1,0.5l0.6,0.7l0.7,0.1l-0.3,1.4l0.5,0.7l0.9,0.2l1.1-0.2l1.2-0.7l1.3-0.1l-0.5,0.8l0.4,0.8l-0.2,0.6l-1.8,0.4l-0.7,1l-0.7,0.8l-0.1,0.8l-1.3,0.3l-0.5,0.8l0.1,0.7l-0.9,2.2l-1.8,1.5l0,0.6l0.4,0.3l1.1,0.1l0.7,0.3l0.1,1l1,1.1l0.6,0.2l0.7,0.7l1.1,0.2l0.9,0.8l0.8-0.5l1.3-0.1l1.1-0.5l0.6-0.8l0.6,0.1l0.9,0.9l1.1,0.3l1.3-0.1l1.3-0.4l1.2,0l0.3,0.6l0.9,0.3l1.9-0.8l1.5-0.3l0.5-0.3l1.3,0.8l0.5-0.1l1.3,0.8l1.8,1.5l1.2,0.4l1-0.3l1.6,0.4l1.5,0l0.2,0.5l0.7-0.8l0.6-0.2l0.5,0.3l0.1,0.6l0.6,0.4l2.1,0.7l1-0.1l0.4,0.3l1.5,0.2l1.1,0.6l0.8,0.7l0.7,0l2-0.9l2.7-0.8l1.5-0.1l2.2,0.3l1,0.6l0.9,0l1.7-0.3l1.5-0.5l1.1-1.2l1.8-0.8l2-1.5l1.2-1.2l1.4-0.9l0.6-1.9l1.1-1.4l1.3-0.7l2.1-1.8l2.1-1.1l1.5-1.2l1.1-1.5l2.1-1.1l1.8-1.1l0.2-0.6l0.6-0.6l0.9-1.5l0.9-0.9l0.5-0.8l1.2-0.6l2.2-1.3l0.8-0.7l1.8-0.8l1.5-0.3l0.7,0l0,0l0.8,0l0.1,0.9l0.4,1l0.1,0.7l-2.1,2.1l-0.7,0.9l0,1l0.7,1.1l0.6,1.6l0.4,0.6l0.3,0.8l0.6,0.9l-0.1,0.9l0.7,0.2l-0.1,0.9l-0.3,0.7l-0.1,0.7l0.3,0.6l1.2,0.1l1.7,0.9l0.3,0.5l-0.5,1.1l-0.2,0.9l-0.8,0.7l0.1,0.7l0.5,0.2l0.8,1.1l1.3,0.9l1,0.2l1.2,0.2l0.9,0.2l1,0.7l0.6,1l0.9,0.9l1,0.6l1.3,0.3l1.2,0.2l0.8,0.3l1.2,0.8l1.4,1.3l0.5,0.7l0.5,0.5l0.5,0.8l1.1,1.3l0.8,0.6l0.9,1.4l-0.5,0.6l1.1,1.1l0.9,0.6l0.9,0.9l0.9,1.1l0.3,0.7L184.4,213.9z", fill: "#D0D0D0" },
        { id: "GBR", d: "M464.5,118.9l-0.5-1.6l-1.9-0.2l-0.5-0.5l2.2-0.9l0.4-1.5l1.6-1.8l1.9,0.4l-0.2,1.7l-0.9,1.5l-1.4,0.7L464.5,118.9z M460.9,110.5l-1,0.4l-0.8-0.3l-0.2-1.1l0.5-1.2l1.2-0.8l1.5,0.3l0.9,0.8l0.1,0.7l-1.1,0.3L460.9,110.5z M473.2,93.5l-3.7,3.1l1.6,1.4l-1.6,2.6l0.6,0.6l-2.5,3.2l0.3,2.8l2.1,3l0,0l-0.8,1.4l0.4,0.5l-1.7,0.4l-0.7,1.3l1.2,1.5l-0.7,1.9l-2-0.2l-2.8,0.7l-1.3-0.8l-1.7,2.3l0.8,1.1l-0.5,1.9l-2,0.2l-0.8,1.1l0.5,0.9l-0.9,0.9l-1.5,0.3l-2.4-0.2l-0.9-0.9l3.3-1.3l-0.2-1.4l-1.7-0.9l2.2-1.1l0.2-1.3l1.3-0.7l0.3-1.9l-0.9-1.1l-1.9-0.2l-0.4-1.6l-1.4-1l-0.6-2.6l0.8-1.2l1.6,0l2.5-1.5l1.9-2.2l-0.2-0.9l0.8-0.9l-0.2-1.7l-1.2-0.6l0.5-1.4l-0.5-0.6l2.2-0.3l2-0.9l1.2,0.1l1.3-0.9l0.8-1.6l1.6-0.6l1.6,0.1l0.9,1.1L473.2,93.5z", fill: "#D0D0D0" },
        { id: "FRA", d: "M488.9,142.2l-0.9-0.3l-1,0.2l-1.2-0.6l-1.2,0.3l-0.1-0.7l-0.9,0.3l-1-0.1l-0.7,1.1h-0.7l-0.5,0.8l-2.4-0.4l-0.8-0.4l0.5-1.3l0.7-1.1l-0.1-0.5l-1-0.5l0.3-1.1l-0.5-0.8l-1.9-0.3l-0.8-0.4l-1-0.1l-1-0.9l-0.3-0.8l0.8-0.6l0.5-1.2l1.3-0.8l1.2,0.5l0.5-0.3l0.1-0.8l1.3-0.4l0.4-1.1l2.2,0.8l1.4-0.1l0.2,0.5l1.3-0.1l1.8,0.8l0.9-0.8l1.4-0.1l0.2-0.5l1.8-0.2l0.3-1l1,0.1l2.1-1.1l-0.9-0.8l-0.1-0.7l1.6-0.4l1.8,0.2l-0.4,0.9l1.1,0.4l-0.2,0.6l0.9,0.5l1,1.9l1.5,0.4l-0.1,0.5l1.4,0.2l0.9-0.6l0.8,0.4l0.6-0.7l1.7,0l0.6,0.8l1.7,0.8l-0.3,1l-2.6,2.2l-0.5,1.2l-1.1,0.3l0.2,0.8l-0.9,0.8l0.3,1l-0.9,0.5l0.5,1.1l-1.7,1.3l-0.8-0.2l-1.1,0.9l-2.1,0.3l-0.2,0.9l-1.6,0.1l-0.4,0.8l-1.9,1.1l-1.5,0.2l-0.1,0.8l-0.9,0.3l-0.2,0.9l-1.3,0.6L488.9,142.2z", fill: "#D0D0D0" },
        { id: "DEU", d: "M489.5,127.9l-1.2,0.5l-1.3-0.1l-0.5,0.8L486,129l-0.3,1.2l-1.4-0.1l-0.5-0.5l-2.2-0.8l-0.1-1.1l-2-0.8l-0.4-0.7l0.2-1.3l-0.9-0.8l0.2-0.4l1.1-0.3l0.6-0.7l2.2,0.9l0.5-0.3l1.7,0.5l1-0.9l2.8,0.3l1.1-0.6l1.1,0.3l0.6-0.3l1.4-0.1l-0.1,1.2l1.3,1.5l-0.9,0.7l0.3,2.2L489.5,127.9z M480.5,120.5l-1.4-0.2l-1.8,0.4l-2.2-0.5l-1.9,0.3l-1-0.3l-0.2-0.6l-1-0.7l-0.3-1.3l0.8-0.7l0.4-0.9l1-0.1l0.5-0.5l0.9,0.4l1.2-0.3l1.4,0.9l1.9,0.2l0.6,0.6l1.8,0.2l0.3,1l-0.5,0.5L480.5,120.5z", fill: "#D0D0D0" },
        { id: "ITA", d: "M497.9,164.8L497.6,165L497,164.4L497.1,163.5L497.9,163.1L498.2,162.6L499.1,161.9L497.9,164.8z M493.3,148.4L493.5,150.7L494.4,151.3L493.8,151.9L493.5,153.1L494.6,153.1L495.4,153.8L496.5,153.7L496.4,152.8L498.6,151.6L501.2,150.9L504.5,149.4L504.1,148.5L503.2,148.8L500.8,148.3L499,149L497.9,147.7L496.5,147.7L495.1,146.2L495.4,145.5L494.3,144.8L493.3,145.8L492.3,145.9L492,144.4L492.8,143.9L492.3,143.5L492.9,142.2L493.8,142.1L493.5,146.1L493.3,148.4z M502.2,141.2L501.5,141.8L502.1,143L502.9,142.2L503.9,142.5L504.4,141.5L503.9,141.1L504.3,140.1L502.2,141.2z", fill: "#D0D0D0" },
        { id: "BRA", d: "M307.7,298.6l-1.5-0.2l-1.7,1.8l-2,1.1l-0.6,1.1l-2-0.6l-2.3,0.8l-0.3,0.9l-1.1,1.5l-1.7-0.2l-1.6,2.9l-1.1,0.1l-0.9,0.8l-1.9-0.2l-2.1,0.9l-1.5-0.6l0.1,2l0.5,1.1l-2.2,2.7l-2.7,3.9l-0.4,1.9l-0.5,1.3l-0.3,3.3l-0.8,0.6l-0.8,1.7l-1.8,2.2l0.6,2.5l-1.9,0.8l-0.3-1.9l-1.2-0.7l-2.2,1.3l-2.1,0.5l-0.9,0.9l-1.5-0.4l-2.1,0.4l-2.7-0.8l-0.8,0.8l0.1,2.6l1.2,0.2l-0.4,1.1l-0.5-0.1l-2.5,1.7l-0.4,1.1l-1.9,0.9l-0.9,1.6l-0.1,1.3l-1.5-0.6l-1.3,0.2l-0.3,0.8l-2.3,1l-0.5-0.3l-1.3,0.6l-1.5-1.9l-1.5-0.3l-0.5-0.9l-1.7-1.3l0.2-0.9l-0.4-1.1l0.2-1.6l-0.7-1.5l-0.3-3.8l-1.6-3.2l0.5-3.9l1.6-4.8l1.9-3.2l-0.1-1.1l1.2-0.6l-0.3-2.4l0.7-1.9l-0.6-1.5l1.6-2.9l0.1-1.7l-1-1.3l0.5-0.9l0.1-1.8l-0.7-1.2l0.5-1.1l-0.1-1.7l-0.9-0.8l0.4-1.2l-2.8-3.8l-1.9-1.5l-2-1l-3.1-3.2l-2.2-1.6l2.3-0.4l0.1-0.7l0.6-1.5l0.1-1.9l0.5-1.8l0.4-0.2l1,0.9l0.7,0.3l0.2,1l1.1,0.1l0.5-1.6l0.5-0.2l0.6,0.5l2.2-0.3l3.3-1.8l0.5,0.3l1.1-0.8l0.3,0.9l0.3-0.5l0.6,0.6l-0.4,1.1l1,1l0.2,1.1l0.6-0.2l0.6,0.7l-0.7,0.9l0.2,0.6l1.7,0.5l1.1-0.5h0.9l0.9,0.9l0.5-0.3l1.6,1l2.3-0.6l1.1,0.2l0.2,0.7l-0.2,1.7l0.3,0.7l1-0.2l0.9-1l1-0.6l1-1.4l0.6-0.2l0.9,0.8l0.5,1.1l1.7,1.3l0.5,0.2l0.4-0.8l0.8-0.4l1.6-0.1l0.6-0.5l3.7,0.2l0.6-0.4l1,0.4l1.4-0.5l1.8,0.3l0.7,0.9l0.6,0.2l2-0.8l0.1-1l1.1-0.7l0.5-1.4l1.9-0.5l0.6-0.8l1.8-0.2l1.2,0.4l1.5-0.3l0.4-0.9l0.8,0l0.5-0.4l0.2-1.8l-0.5-1.2l0.3-1.1l-0.9-1l-0.1-0.9l-0.9-0.5l-0.2-1.1l-0.4-0.6l0.1-0.8l2-0.4l1.3,0.1l1.8-0.9l0.5,0.2l0.8-0.5l3,1.6l0.5-0.1l0.8,0.7l1.4-0.2l0.7-0.7l0.8,0.2h1.2l0.9-0.9l0.2-1.8l0.5-0.9l-0.3-0.6l1.3-0.3l0.7-1.3l1.2-0.7l0.7,0.2l1.8-0.5l0.8,0.3l1.7-0.5l0.8,0.3l0.8-0.4h0.8l0.7-1.1l1.3-0.6l0.2-0.6l0.7-0.3l2.7,0l2.8-0.7l0.8,0.2l0.6-0.5l0.5,0.2l0.5-0.6l2.2-0.9l1,0.3l0.9-0.2l0.5-1.5l-0.3-0.6l0.5-0.2l0.3-1.2l-0.8-0.7l0.7-0.7l-0.5-0.6l0.3-1.3L307.7,298.6z", fill: "#D0D0D0" },
        { id: "ARG", d: "M292.6,389.8l0.1-2.2l-0.8-1.5l2.9-1.7l1.8-1.5l-0.1-0.6l1.2-0.2l3.4-3.5l0.7-2.9l-0.2-2.9l1.1-2.1l-0.2-1.1l-1.8-1.1l-3.1-0.1l-2.2-0.8l-4.8-0.2l-2.6-1.1l-0.5,0.3l-0.7-1.7l-0.3,0.6l-0.9-0.4l-0.1,1.5l-1.7,0.6l-1.6-0.8l-0.9,0.8l0.2,1.9l-2.2,2.2l-1.7,0.1l-1.6,1.6l0,5.2l1.4,2.2l-2.1,2.4l-2.2,1.3l-0.6,2.4l0.8,2.2l-0.4,2.1l0.8,1.2l-0.2,1.7l2.2,1.8l-1.6,1.2l-2.8,0.8l-1.2-0.8l-1.1,0.2l-2.6-0.5l-1.2,0.1l-1.3-0.8l-0.2,1.9l-0.7,0.2l-2.9-0.4l-0.4-0.9l-3.1-1.4l-2.4-1.7l3-1l-0.3-0.6l2.9-1.5l-0.8-0.7l0.3-1.3l-0.3-0.5l-1.8-0.1l0.8-1.9l1.4-0.9l-0.2-0.7l2.9-1.6l1.9-0.1l2.2-1l0.9-1.7l-0.2-3.9l1.9-1.8l0.8-2.5l-0.2-2.4l1.1-0.3l-0.3-1.4l0.8-1.5l1.4-1.1l0.4-1.1l1.3-0.9l-0.3-1.2l-2.6,1.2l-1.6-0.2l-0.5-0.6l1.9-1.5l-0.2-0.6l-4-0.2l-3.1,0.3l-2.2-0.1l-0.5,0.4l0.1,1.5l-0.9,0l-0.5,0.6l-0.1-2.1l2-0.5l0.3-0.8l-0.4-1.4l-1.2-0.3l0.7-1.9l-0.2-1.5l-2.3-1.2l-1.9,0.7l-3.1-0.5l-2.2,0.7l-0.3-0.4l-1.1-0.2l-1.9-1.1l-0.2-1.1l-0.9-0.4l0-0.9l-1,0.1l-0.7-0.9l-0.1-4.7l0.8-0.5l-0.4-3.5l1.1-0.5l-0.7-3.1l0.9-3.1l2.6-2.5l-0.3-1.1l1.7-2.5l0.3-1.3l-0.7-2.9l0.1-1.8l-1.2-2.4l1.5-3.8l1-0.1l1.8-2l-0.2-0.8l1.5-2.8l0,0l2.2,0.2l0.9-0.9l2.6,0.2l0.9,0.5l1.4,3.1l0.3,3.8l0.7,1.5l-0.2,1.6l0.4,1.1l-0.2,0.9l1.7,1.3l0.5,0.9l1.5,0.3l1.5,1.9l1.3-0.6l0.5,0.3l2.3-1l0.3-0.8l1.3-0.2l1.5,0.6l0.1-1.3l0.9-1.6l1.9-0.9l0.4-1.1l2.5-1.7l0.5,0.1l0.4-1.1l-1.2-0.2l-0.1-2.6l0.8-0.8l2.7,0.8l2.1-0.4l1.5,0.4l0.9-0.9l2.1-0.5l2.2-1.3l1.2,0.7l0.3,1.9l1.9-0.8l-0.6-2.5l1.8-2.2l0.8-1.7l0.8-0.6l0.3-3.3l0.5-1.3l0.4-1.9l2.7-3.9l2.2-2.7l-0.5-1.1l-0.1-2l1.5,0.6l2.1-0.9l1.9,0.2l0.9-0.8l1.1-0.1l1.6-2.9l1.7,0.2l1.1-1.5l0.3-0.9l2.3-0.8l2,0.6l0.6-1.1l2-1.1l1.7-1.8l1.5,0.2l3.2,1.3l0.9,0.7l-0.4,0.9l1.1,1l-0.2,1.5l-1.2,2.1l0.8,0.5l-0.5,1.4l0.2,1.2l1.2,0.3l-0.1,0.6l-2,1.9l-0.2,1.2l0.5,1.2l-0.4,0.8l0.5,1.6l1.5,0.7l0.3,1.4l0.1,2.7l-0.4,1l0.5,1.3l0.1,1.8l-1.8,1.4l-2.2-0.2l-2.2,1.3l-1.8,2l-0.4,3.3l0.3,2.7l-0.5,2.6l-0.5,1.4l-0.1,4.9l1.1,6.1l0.7,1.2l-0.2,2.5l-0.6,1.4l0.9,1.1l0.1,0.9l-2.8,1.5l-1.9,2.3l-2.5,0l-1.1-0.8l0.1-2.3l-1.5-0.2l-0.7,0.2l-0.3,1.8l-1.6,3.7l0,1.5l-2.1,1.2l0.1,1.6l-0.8,1.1l0.4,1.3l-0.2,1.5L292.6,389.8z", fill: "#D0D0D0" }
    ]
};


// Colors for charts
const COLORS = ['#0a0a70', '#8a8aff'];
const BAR_COLOR = '#0a0a70';




// UserDemographics bileşeni
const UserDemographics = () => {
    const [selectedTab, setSelectedTab] = useState("all_users");
    const [activeTooltipCountry, setActiveTooltipCountry] = useState(null);
    const mapRef = useRef(null);

    // Function to handle map country hover
    const handleCountryHover = (countryId) => {
        setActiveTooltipCountry(locationData.find(item => item.country === countryId));
    };

    return (



        <div className="min-h-screen bg-gray-50 p-6">
            <div className="max-w-7xl mx-auto space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl font-bold">User Demographics</h1>
                        <p className="text-gray-500 mt-1">
                            User demographics help businesses and organizations understand their audience better and tailor their offerings to meet their needs
                        </p>
                    </div>
                    <div className="header-actions">
                        <Button variant="outline" size="sm" className="header-action-button">
                            <Calendar className="w-4 h-4" />
                            Last 7 days
                        </Button>
                        <Button variant="outline" size="sm" className="header-action-button">
                            <Filter className="w-4 h-4" />
                            Filter
                        </Button>
                        <Button variant="outline" size="sm" className="header-action-button">
                            <Share className="w-4 h-4" />
                            Share
                        </Button>
                        <Button variant="outline" size="sm" className="header-action-button">
                            <MoreHorizontal className="w-4 h-4" />
                        </Button>
                    </div>
                </div>

                {/* Top Row: Age, Gender, Geography */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* Age Chart */}
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-lg">Age</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="h-64">
                                <ResponsiveContainer width="100%" height="100%">
                                    <BarChart
                                        layout="vertical"
                                        data={ageData}
                                        margin={{ top: 5, right: 30, left: 30, bottom: 5 }}
                                    >
                                        <XAxis type="number" domain={[0, 800]} />
                                        <YAxis dataKey="age" type="category" width={50} />
                                        <Tooltip />
                                        <Bar dataKey="value" fill={BAR_COLOR} />
                                    </BarChart>
                                </ResponsiveContainer>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Gender Chart */}
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-lg">Gender</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="h-64 flex flex-col items-center">
                                <ResponsiveContainer width="100%" height="100%">
                                    <PieChart>
                                        <Pie
                                            data={genderData}
                                            cx="50%"
                                            cy="50%"
                                            innerRadius={60}
                                            outerRadius={90}
                                            paddingAngle={0}
                                            dataKey="value"
                                        >
                                            {genderData.map((entry, index) => (
                                                <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                                            ))}
                                        </Pie>
                                        <Tooltip />
                                    </PieChart>
                                </ResponsiveContainer>
                                <div className="flex justify-center gap-6 mt-2">
                                    <div className="flex items-center gap-2">
                                        <div className="h-3 w-3 bg-[#0a0a70] rounded-sm"></div>
                                        <span className="text-sm">Male - 750 (30%)</span>
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <div className="h-3 w-3 bg-[#8a8aff] rounded-sm"></div>
                                        <span className="text-sm">Female - 1,740 (70%)</span>
                                    </div>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    {/* Geography Chart */}
                    <Card>
                        <CardHeader className="pb-2">
                            <CardTitle className="text-lg">Geography</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="h-64">
                                <ResponsiveContainer width="100%" height="100%">
                                    <BarChart
                                        layout="vertical"
                                        data={geographyData}
                                        margin={{ top: 5, right: 30, left: 50, bottom: 5 }}
                                    >
                                        <XAxis type="number" domain={[0, 3000]} />
                                        <YAxis dataKey="language" type="category" width={70} />
                                        <Tooltip />
                                        <Bar dataKey="value" fill={BAR_COLOR} />
                                    </BarChart>
                                </ResponsiveContainer>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Location Chart */}
                <Card>
                    <CardHeader>
                        <CardTitle className="text-lg">Location</CardTitle>
                    </CardHeader>
                    <CardContent>


                        {/* Location content */}
                        <div className="location-content">
                            <Tabs defaultValue="all_users" className="w-full" value={selectedTab} onValueChange={setSelectedTab}>
                                <TabsList className="mb-6">
                                    <TabsTrigger className="location-tab active" value="all_users">All users</TabsTrigger>
                                    <TabsTrigger className="location-tab" value="new_users">New Users</TabsTrigger>
                                    <TabsTrigger className="location-tab" value="active_users">Active Users</TabsTrigger>
                                </TabsList>

                                <TabsContent value="all_users" className="space-y-4">
                                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                                        {/* Bar Chart */}
                                        <div className="h-96">
                                            <ResponsiveContainer width="100%" height="100%">
                                                <BarChart
                                                    layout="vertical"
                                                    data={locationData}
                                                    margin={{ top: 5, right: 30, left: 80, bottom: 5 }}
                                                >
                                                    <XAxis type="number" />
                                                    <YAxis dataKey="country" type="category" width={80} />
                                                    <Tooltip />
                                                    <Bar dataKey="users" fill={BAR_COLOR} />
                                                </BarChart>
                                            </ResponsiveContainer>
                                        </div>

                                        {/* World Map */}
                                        <div className="h-96 bg-gray-100 rounded-md relative overflow-hidden">
                                            <div ref={mapRef} className="w-full h-full">
                                                <svg
                                                    viewBox={worldMapData.viewBox}
                                                    className="w-full h-full"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                >
                                                    {worldMapData.countries.map((country) => (
                                                        <path
                                                            key={country.id}
                                                            d={country.d}
                                                            fill={country.id === activeTooltipCountry?.country ? '#0a0a70' : '#D0D0D0'}
                                                            stroke="#FFFFFF"
                                                            strokeWidth="0.5"
                                                            onMouseEnter={() => handleCountryHover(country.id)}
                                                            onMouseLeave={() => setActiveTooltipCountry(null)}
                                                            className="transition-colors hover:fill-blue-800 cursor-pointer"
                                                        />
                                                    ))}

                                                    {/* User location markers */}
                                                    {locationData.map((location) => (
                                                        location.coords && location.coords.lat !== 0 && (
                                                            <circle
                                                                key={location.country}
                                                                cx={500 + (location.coords.lng * 500 / 180)}
                                                                cy={255 - (location.coords.lat * 250 / 90)}
                                                                r={Math.sqrt(location.users) / 8}
                                                                fill="#0a0a70"
                                                                fillOpacity="0.6"
                                                                stroke="#FFFFFF"
                                                                strokeWidth="0.5"
                                                                onMouseEnter={() => handleCountryHover(location.country)}
                                                                onMouseLeave={() => setActiveTooltipCountry(null)}
                                                                className="cursor-pointer hover:fill-opacity-90"
                                                            />
                                                        )
                                                    ))}
                                                </svg>

                                                {/* Tooltip */}
                                                {activeTooltipCountry && (
                                                    <div className="absolute top-4 right-4 bg-white p-3 rounded-md shadow-lg border border-gray-200">
                                                        <h3 className="font-semibold">{activeTooltipCountry.country}</h3>
                                                        <p className="text-sm text-gray-600 mt-1">Users: {activeTooltipCountry.users.toLocaleString()}</p>
                                                        <p className="text-sm text-gray-600">Active: {activeTooltipCountry.active.toLocaleString()}</p>
                                                    </div>
                                                )}
                                            </div>
                                        </div>
                                    </div>


                                </TabsContent>

                                <TabsContent value="new_users">
                                    <div className="h-96 flex items-center justify-center">
                                        <p className="text-gray-500">New users data would be displayed here</p>
                                    </div>
                                </TabsContent>

                                <TabsContent value="active_users">
                                    <div className="h-96 flex items-center justify-center">
                                        <p className="text-gray-500">Active users data would be displayed here</p>
                                    </div>
                                </TabsContent>
                            </Tabs>
                        </div>
                    </CardContent>
                </Card>
            </div>
        </div>
    );
};


const lineChartData = [
    { day: '01 Aug', users: 800, activeUsers: 1500 },
    { day: '02 Aug', users: 1100, activeUsers: 1800 },
    { day: '03 Aug', users: 1400, activeUsers: 1300 },
    { day: '04 Aug', users: 850, activeUsers: 1600 },
    { day: '05 Aug', users: 1700, activeUsers: 1750 },
    { day: '06 Aug', users: 2700, activeUsers: 1900 },
    { day: '07 Aug', users: 1900, activeUsers: 850 },
];

const barChartData = [
    { day: '01', last7Days: 1.5, previousPeriod: 2.1 },
    { day: '02', last7Days: 1.8, previousPeriod: 2.3 },
    { day: '03', last7Days: 2.1, previousPeriod: 1.9 },
    { day: '04', last7Days: 2.4, previousPeriod: 0.8 },
    { day: '05', last7Days: 2.8, previousPeriod: 0.6 },
    { day: '06', last7Days: 0.9, previousPeriod: 0.7 },
    { day: '07', last7Days: 1.2, previousPeriod: 2.5 },
];

// Technology Adoption data
const platformData = [
    { name: "Windows", value: 10, color: "#000067" },
    { name: "macOS", value: 10, color: "#1a1a8a" },
    { name: "iOS", value: 10, color: "#3333ad" },
    { name: "Android", value: 10, color: "#4d4dd0" },
    { name: "Linux", value: 10, color: "#6666f3" },
    { name: "Other", value: 10, color: "#8080ff" },
];

const utmSourceData = [
    { name: "Direct", value: 10, color: "#000067" },
    { name: "Google", value: 10, color: "#1a1a8a" },
    { name: "Facebook", value: 10, color: "#3333ad" },
    { name: "Twitter", value: 10, color: "#4d4dd0" },
    { name: "LinkedIn", value: 10, color: "#6666f3" },
    { name: "Other", value: 10, color: "#8080ff" },
];

const DashboardOverview = () => {
    return (
        <div className="dashboard">
            <link
                rel="stylesheet"
                href="https://cdn.jsdelivr.net/npm/@tabler/icons-webfont@latest/dist/tabler-icons.min.css"
            />

            <DashboardHeader />

            <div className="dashboard-content">
                <UserDemographics />

                {/* Technology Adoption Section */}
                <div className="tech-adoption-section">
                    <div className="tech-adoption-header">
                        <div>
                            <h2 className="tech-adoption-title">Technology Adoption</h2>
                            <p className="tech-adoption-description">
                                The types of devices (e.g., smartphones, tablets, computers) and operating
                                systems that users use to access the product or service
                            </p>
                        </div>
                        <div className="tech-adoption-actions">
                            <button className="tech-action-btn">
                                Last 7 days
                                <Calendar size={16} className="action-icon" />
                            </button>
                            <button className="tech-action-btn">
                                <Filter size={16} className="action-icon" />
                                Filter
                            </button>
                            <button className="tech-action-btn">
                                <Share size={16} className="action-icon" />
                                Share
                            </button>
                            <button className="tech-action-more">
                                <MoreHorizontal size={16} />
                            </button>
                        </div>
                    </div>

                    <div className="tech-charts-container">
                        <div className="tech-chart-card">
                            <h3 className="tech-chart-title">Platform</h3>
                            <div className="tech-chart">
                                <ResponsiveContainer width="100%" height={200}>
                                    <PieChart>
                                        <Pie
                                            data={platformData}
                                            cx="50%"
                                            cy="50%"
                                            innerRadius={60}
                                            outerRadius={80}
                                            paddingAngle={0}
                                            dataKey="value"
                                        >
                                            {platformData.map((entry, index) => (
                                                <Cell key={`cell-${index}`} fill={entry.color} />
                                            ))}
                                        </Pie>
                                    </PieChart>
                                </ResponsiveContainer>
                                <div className="tech-chart-legend">
                                    {platformData.map((item, index) => (
                                        <div key={index} className="tech-legend-item">
                                            <div className="tech-legend-color" style={{ backgroundColor: item.color }}></div>
                                            <span className="tech-legend-name">{item.name}</span>
                                            <span className="tech-legend-value">{item.value}%</span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>

                        <div className="tech-chart-card">
                            <h3 className="tech-chart-title">UTM Source</h3>
                            <div className="tech-chart">
                                <ResponsiveContainer width="100%" height={200}>
                                    <PieChart>
                                        <Pie
                                            data={utmSourceData}
                                            cx="50%"
                                            cy="50%"
                                            innerRadius={60}
                                            outerRadius={80}
                                            paddingAngle={0}
                                            dataKey="value"
                                        >
                                            {utmSourceData.map((entry, index) => (
                                                <Cell key={`cell-${index}`} fill={entry.color} />
                                            ))}
                                        </Pie>
                                    </PieChart>
                                </ResponsiveContainer>
                                <div className="tech-chart-legend">
                                    {utmSourceData.map((item, index) => (
                                        <div key={index} className="tech-legend-item">
                                            <div className="tech-legend-color" style={{ backgroundColor: item.color }}></div>
                                            <span className="tech-legend-name">{item.name}</span>
                                            <span className="tech-legend-value">{item.value}%</span>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div className="dashboard-overview">
                    {/* Header */}
                    <div className="dashboard-header">
                        <h1 className="dashboard-title">BrandX Microsite Overview</h1>
                        <div className="dashboard-actions">
                            <button className="action-button">
                                <Calendar className="icon" />
                                Last 7 days
                            </button>
                            <button className="action-button">
                                <Filter className="icon" />
                                Filter
                            </button>
                            <button className="action-button">
                                <Share className="icon" />
                                Share
                            </button>
                            <button className="action-button">
                                <MoreHorizontal className="icon" />
                            </button>
                        </div>
                    </div>

                    <div className="dashboard-grid">
                        {/* Metrics and Line Chart */}
                        <div className="main-card">
                            <div className="metrics-row">
                                <div className="metric-card">
                                    <p className="metric-title">Last 7 days Users</p>
                                    <div className="metric-value-container">
                                        <span className="metric-value">2,490</span>
                                        <div className="metric-change positive">
                                            <TrendingUp className="icon-small" />
                                            5%
                                        </div>
                                    </div>
                                </div>
                                <div className="metric-card">
                                    <p className="metric-title">Previous period Active Users</p>
                                    <div className="metric-value-container">
                                        <span className="metric-value">1,980</span>
                                        <div className="metric-change negative">
                                            <TrendingDown className="icon-small" />
                                            25%
                                        </div>
                                    </div>
                                </div>
                                <div className="metric-card">
                                    <p className="metric-title">New Users</p>
                                    <div className="metric-value-container">
                                        <span className="metric-value">490</span>
                                        <div className="metric-change positive">
                                            <TrendingUp className="icon-small" />
                                            12%
                                        </div>
                                    </div>
                                </div>
                                <div className="metric-card">
                                    <p className="metric-title">Avg Time On Page</p>
                                    <div className="metric-value-container">
                                        <span className="metric-value">2m16s</span>
                                        <div className="metric-change negative">
                                            <TrendingDown className="icon-small" />
                                            25%
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {/* Line Chart */}
                            <div className="chart-container">
                                <ResponsiveContainer width="100%" height="100%">
                                    <LineChart data={lineChartData}>
                                        <XAxis
                                            dataKey="day"
                                            axisLine={false}
                                            tickLine={false}
                                            tick={{ fontSize: 12, fill: '#666' }}
                                        />
                                        <YAxis
                                            axisLine={false}
                                            tickLine={false}
                                            tick={{ fontSize: 12, fill: '#666' }}
                                            domain={[0, 3000]}
                                        />
                                        <Line
                                            type="monotone"
                                            dataKey="users"
                                            stroke="#3b82f6"
                                            strokeWidth={2}
                                            dot={{ fill: '#3b82f6', r: 4 }}
                                        />
                                        <Line
                                            type="monotone"
                                            dataKey="activeUsers"
                                            stroke="#94a3b8"
                                            strokeWidth={2}
                                            dot={{ fill: '#94a3b8', r: 4 }}
                                        />
                                        <Legend
                                            align="left"
                                            verticalAlign="bottom"
                                            iconType="circle"
                                            wrapperStyle={{ paddingTop: '10px' }}
                                        />
                                    </LineChart>
                                </ResponsiveContainer>
                            </div>
                        </div>

                        {/* Bar Chart */}
                        <div className="side-card">
                            <div className="card-header">
                                <h2 className="card-title">Avg Time On Page</h2>
                                <div className="metric-value-container">
                                    <span className="metric-value">2m16s</span>
                                    <div className="metric-change negative">
                                        <TrendingDown className="icon-small" />
                                        25%
                                    </div>
                                </div>
                            </div>
                            <div className="chart-container">
                                <ResponsiveContainer width="100%" height="100%">
                                    <BarChart data={barChartData}>
                                        <XAxis
                                            dataKey="day"
                                            axisLine={false}
                                            tickLine={false}
                                            tick={{ fontSize: 12, fill: '#666' }}
                                        />
                                        <YAxis
                                            axisLine={false}
                                            tickLine={false}
                                            tick={{ fontSize: 12, fill: '#666' }}
                                            domain={[0, 3]}
                                        />
                                        <Bar dataKey="last7Days" fill="#3b82f6" />
                                        <Bar dataKey="previousPeriod" fill="#94a3b8" />
                                    </BarChart>
                                </ResponsiveContainer>
                            </div>

                            {/* Bar Chart Legend */}
                            <div className="chart-legend">
                                <div className="legend-item">
                                    <div className="legend-color blue"></div>
                                    <span className="legend-text">Last 7 days</span>
                                </div>
                                <div className="legend-item">
                                    <div className="legend-color gray"></div>
                                    <span className="legend-text">Previous period</span>
                                </div>
                            </div>

                            {/* Average line indicator */}
                            <div className="average-indicator">
                                <div className="average-text">Avg 2m16s</div>
                                <div className="average-line"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default DashboardOverview;
