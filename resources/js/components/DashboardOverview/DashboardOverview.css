/* Sub Navigation Styles */
.sub-navigation {
  background-color: #f9fafb;
  padding: 0.5rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.sub-nav-container {
  display: flex;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  margin-right: 1rem;
  cursor: pointer;
}

.back-button:hover {
  background-color: #f3f4f6;
}

.back-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: #6b7280;
}

.sub-nav-tabs {
  display: flex;
  gap: 0.5rem;
}

.sub-nav-tab {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  background: transparent;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #4b5563;
  cursor: pointer;
}

.sub-nav-tab.active {
  background-color: #eef2ff;
  color: #000067;
}

.tab-icon {
  font-size: 1rem;
}

.dashboard-overview {
  padding: 24px;
  max-width: 1200px;
  margin: 0 auto;
  font-family: var(--font-family-secondary);
}

.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24px;
}

.dashboard-title {
  font-size: 24px;
  font-weight: 700;
  margin: 0;
}

.dashboard-actions {
  display: flex;
  gap: 8px;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.icon {
  width: 16px;
  height: 16px;
}

.icon-small {
  width: 14px;
  height: 14px;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
}

.main-card, .side-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 24px;
}

.metrics-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 24px;
  margin-bottom: 32px;
}

.metric-card {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.metric-title {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.metric-value-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: 700;
}

.metric-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
}

.positive {
  color: #10b981;
}

.negative {
  color: #ef4444;
}

.chart-container {
  height: 250px;
  margin-top: 16px;
}

.card-header {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  margin: 0;
}

.chart-legend {
  display: flex;
  gap: 16px;
  margin-top: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.blue {
  background-color: #3b82f6;
}

.gray {
  background-color: #94a3b8;
}

.legend-text {
  font-size: 12px;
  color: #666;
}

.average-indicator {
  margin-top: 16px;
  text-align: center;
}

.average-text {
  font-size: 14px;
  color: #3b82f6;
  font-weight: 500;
}

.average-line {
  height: 1px;
  background-color: #3b82f6;
  margin-top: 4px;
}

@media (max-width: 768px) {
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .metrics-row {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .metrics-row {
    grid-template-columns: 1fr;
  }
  
  .dashboard-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
}

/* Technology Adoption Section */
.tech-adoption-section {
  max-width: 1200px;
  margin: 2rem auto;
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 1.5rem;
}

.tech-adoption-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1.5rem;
}

.tech-adoption-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 0.5rem 0;
}

.tech-adoption-description {
  font-size: 0.875rem;
  color: #6b7280;
  margin: 0;
  max-width: 600px;
}

.tech-adoption-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.tech-action-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  color: #4b5563;
  cursor: pointer;
}

.tech-action-more {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 0.375rem;
  cursor: pointer;
}

.action-icon {
  color: #6b7280;
}

.tech-charts-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1.5rem;
}

.tech-chart-card {
  background-color: white;
  border: 1px solid #e0e0e0;
  border-radius: 0.375rem;
  padding: 1.5rem;
}

.tech-chart-title {
  font-size: 1rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 1rem 0;
}

.tech-chart {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.tech-chart-legend {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-top: 1rem;
  width: 100%;
  max-width: 300px;
}

.tech-legend-item {
  display: flex;
  align-items: center;
  font-size: 0.75rem;
}

.tech-legend-color {
  width: 0.75rem;
  height: 0.75rem;
  border-radius: 50%;
  margin-right: 0.5rem;
}

.tech-legend-name {
  flex: 1;
  color: #111827;
}

.tech-legend-value {
  font-weight: 500;
  color: #111827;
}

/* Responsive adjustments */
@media (max-width: 1024px) {
  .tech-charts-container {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .tech-adoption-header {
    flex-direction: column;
    gap: 1rem;
  }
  
  .tech-adoption-actions {
    width: 100%;
    overflow-x: auto;
    padding-bottom: 0.5rem;
  }
}

