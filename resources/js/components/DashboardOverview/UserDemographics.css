.min-h-screen {
  min-height: 100vh;
}

.bg-gray-50 {
  background-color: #f9fafb;
}

.p-6 {
  padding: 1.5rem;
}

.max-w-7xl {
  max-width: 80rem;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.space-y-6 > * + * {
  margin-top: 1.5rem;
}

.flex {
  display: flex;
}

.items-center {
  align-items: center;
}

.justify-between {
  justify-content: space-between;
}

.gap-2 {
  gap: 0.5rem;
}

.gap-6 {
  gap: 1.5rem;
}

.text-2xl {
  font-size: 1.5rem;
}

.font-bold {
  font-weight: 700;
}

.text-gray-500 {
  color: #6b7280;
}

.mt-1 {
  margin-top: 0.25rem;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.md\:grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.lg\:grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.h-64 {
  height: 16rem;
}

.h-96 {
  height: 24rem;
}

.w-full {
  width: 100%;
}

.text-lg {
  font-size: 1.125rem;
}

.text-sm {
  font-size: 0.875rem;
}

.pb-2 {
  padding-bottom: 0.5rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mb-6 {
  margin-bottom: 1.5rem;
}

.rounded-sm {
  border-radius: 0.125rem;
}

.rounded-md {
  border-radius: 0.375rem;
}

.bg-\[\#0a0a70\] {
  background-color: #0a0a70;
}

.bg-\[\#8a8aff\] {
  background-color: #8a8aff;
}

.bg-gray-100 {
  background-color: #f3f4f6;
}

.relative {
  position: relative;
}

.overflow-hidden {
  overflow: hidden;
}

.transition-colors {
  transition-property: color, background-color, border-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.hover\:fill-blue-800:hover {
  fill: #1e40af;
}

.cursor-pointer {
  cursor: pointer;
}

/* Card styles */
.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.card-header {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e5e7eb;
}

.card-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #111827;
}

.card-content {
  padding: 1.5rem;
}

/* Tabs styles */
.tabs {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.tabs-list {
  display: inline-flex;
  height: 2.5rem;
  align-items: center;
  justify-content: center;
  border-radius: 0.375rem;
  background-color: #f3f4f6;
  padding: 0.25rem;
  margin-bottom: 1.5rem;
}

.tabs-trigger {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  border-radius: 0.25rem;
  padding: 0.375rem 0.75rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  background: transparent;
  border: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tabs-trigger[data-state="active"] {
  background-color: white;
  color: #111827;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.tabs-content {
  margin-top: 0.5rem;
  padding-top: 0.5rem;
}

/* Button styles - updated */
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  padding: 0.5rem 1rem;
  cursor: pointer;
  transition: background-color 0.2s, border-color 0.2s;
}

.button-outline {
  background-color: white;
  border: 1px solid #e5e7eb;
  color: #374151;
}

.button-outline:hover {
  background-color: #f9fafb;
}

/* Header action buttons */
.header-actions {
  display: flex;
  gap: 0.5rem;
}

.header-action-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  padding: 0.5rem 0.75rem;
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
}

.header-action-button:hover {
  background-color: #f9fafb;
}

.header-action-button svg {
  width: 1rem;
  height: 1rem;
}

/* Location tabs specific styles */
.location-tabs-list {
  display: inline-flex;
  height: 2.5rem;
  align-items: center;
  border-radius: 0.25rem;
  background-color: #f3f4f6;
  padding: 0.25rem;
  margin-bottom: 1rem;
  width: auto;
}

.location-tab {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  height: 2rem;
  padding: 0 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: #6b7280;
  background: transparent;
  border: none;
  border-radius: 0.125rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.location-tab.active {
  background-color: white;
  color: #111827;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.location-tab:hover:not(.active) {
  color: #374151;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .md\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
  
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}


