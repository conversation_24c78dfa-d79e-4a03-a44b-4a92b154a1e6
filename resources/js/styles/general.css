/* PfizerDiatype Font Tanımlamaları */@font-face {
  font-family: 'PfizerDiatype';  src: url('../assets/fonts/PfizerDiatype-Thin.ttf') format('truetype');
  font-weight: 100;  font-style: normal;
  font-display: swap;}
@font-face {
  font-family: 'PfizerDiatype';  src: url('../assets/fonts/PfizerDiatype-ThinItalic.ttf') format('truetype');
  font-weight: 100;  font-style: italic;
  font-display: swap;}
@font-face {
  font-family: 'PfizerDiatype';  src: url('../assets/fonts/PfizerDiatype-Light.ttf') format('truetype');
  font-weight: 300;  font-style: normal;
  font-display: swap;}
@font-face {
  font-family: 'PfizerDiatype';  src: url('../assets/fonts/PfizerDiatype-LightItalic.ttf') format('truetype');
  font-weight: 300;  font-style: italic;
  font-display: swap;}
@font-face {
  font-family: 'PfizerDiatype';  src: url('../assets/fonts/PfizerDiatype-Regular.ttf') format('truetype');
  font-weight: 400;  font-style: normal;
  font-display: swap;}
@font-face {
  font-family: 'PfizerDiatype';  src: url('../assets/fonts/PfizerDiatype-RegularItalic.ttf') format('truetype');
  font-weight: 400;  font-style: italic;
  font-display: swap;}
@font-face {
  font-family: 'PfizerDiatype';  src: url('../assets/fonts/PfizerDiatype-Medium.ttf') format('truetype');
  font-weight: 500;  font-style: normal;
  font-display: swap;}
@font-face {
  font-family: 'PfizerDiatype';  src: url('../assets/fonts/PfizerDiatype-MediumItalic.ttf') format('truetype');
  font-weight: 500;  font-style: italic;
  font-display: swap;}
@font-face {
  font-family: 'PfizerDiatype';  src: url('../assets/fonts/PfizerDiatype-Bold.ttf') format('truetype');
  font-weight: 700;  font-style: normal;
  font-display: swap;}
@font-face {
  font-family: 'PfizerDiatype';  src: url('../assets/fonts/PfizerDiatype-BoldItalic.ttf') format('truetype');
  font-weight: 700;  font-style: italic;
  font-display: swap;}
@font-face {
  font-family: 'PfizerDiatype';  src: url('../assets/fonts/PfizerDiatype-Heavy.ttf') format('truetype');
  font-weight: 800;  font-style: normal;
  font-display: swap;}
@font-face {
  font-family: 'PfizerDiatype';  src: url('../assets/fonts/PfizerDiatype-HeavyItalic.ttf') format('truetype');
  font-weight: 800;  font-style: italic;
  font-display: swap;}
@font-face {
  font-family: 'PfizerDiatype';  src: url('../assets/fonts/PfizerDiatype-Black.ttf') format('truetype');
  font-weight: 900;  font-style: normal;
  font-display: swap;}
@font-face {
  font-family: 'PfizerDiatype';  src: url('../assets/fonts/PfizerDiatype-BlackItalic.ttf') format('truetype');
  font-weight: 900;  font-style: italic;
  font-display: swap;}
/* PfizerTomorrow Font Tanımlamaları */
@font-face {  font-family: 'PfizerTomorrow';
  src: url('../assets/fonts/PfizerTomorrow-Regular.ttf') format('truetype');  font-weight: 400;
  font-style: normal;  font-display: swap;
}
@font-face {  font-family: 'PfizerTomorrow';
  src: url('../assets/fonts/PfizerTomorrow-RegularItalic.ttf') format('truetype');  font-weight: 400;
  font-style: italic;  font-display: swap;
}
@font-face {  font-family: 'PfizerTomorrow';
  src: url('../assets/fonts/PfizerTomorrow-Bold.ttf') format('truetype');  font-weight: 700;
  font-style: normal;  font-display: swap;
}
@font-face {  font-family: 'PfizerTomorrow';
  src: url('../assets/fonts/PfizerTomorrow-BoldItalic.ttf') format('truetype');  font-weight: 700;
  font-style: italic;  font-display: swap;
}
@font-face {  font-family: 'PfizerTomorrow';
  src: url('../assets/fonts/PfizerTomorrow-Black.ttf') format('truetype');  font-weight: 900;
  font-style: normal;  font-display: swap;
}
@font-face {  font-family: 'PfizerTomorrow';
  src: url('../assets/fonts/PfizerTomorrow-BlackItalic.ttf') format('truetype');  font-weight: 900;
  font-style: italic;  font-display: swap;
}
/* Genel stil tanımlamaları */
:root {
  --primary-color: #000066;
  --secondary-color: #1a73e8;
  --text-color: #333333;
  --light-text-color: #666666;
  --background-color: #ffffff;
  --light-background: #f5f5f5;
  --border-color: #e0e0e0;
  --font-family-primary: 'PfizerTomorrow', Georgia, serif;
  --font-family-secondary: 'PfizerDiatype', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}
* {
  box-sizing: border-box;}
html, body {
  height: 100%;  margin: 0;
  padding: 0;}
body {
  font-family: var(--font-family-primary);  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;  color: var(--text-color);
  font-size: 16px;  line-height: 1.5;
}
h1, h2, h3, h4, h5, h6 {  font-family: var(--font-family-primary);
  margin: 0 0 0.5em 0;  line-height: 1.2;
}
h1 {  font-size: 2.5rem;
  font-weight: 700;}
h2 {
  font-size: 2rem;  font-weight: 700;
}
h3 {  font-size: 1.75rem;
  font-weight: 600;}
h4 {
  font-size: 1.5rem;  font-weight: 600;
}
h5 {  font-size: 1.25rem;
  font-weight: 500;}
h6 {
  font-size: 1rem;  font-weight: 500;
}
p {  margin: 0 0 1em 0;
}
a {  color: var(--primary-color);
  text-decoration: none;  transition: color 0.2s ease;
}
a:hover {  text-decoration: underline;
}
button, input, select, textarea {  font-family: var(--font-family-primary);
  font-size: 1rem;}
button {
  cursor: pointer;}
/* Form elemanları */
input, textarea, select {  padding: 12px 16px;
  border: 1px solid var(--border-color);  border-radius: 4px;
  background-color: var(--light-background);  width: 100%;
  font-size: 16px;  transition: border-color 0.3s, box-shadow 0.3s;
}
input:focus, textarea:focus, select:focus {  border-color: var(--primary-color);
  outline: none;  box-shadow: 0 0 0 2px rgba(0, 0, 102, 0.1);
}
label {  display: block;
  margin-bottom: 8px;  font-weight: 500;
}
/* Butonlar */.btn {
  display: inline-block;  padding: 12px 24px;
  border: none;  border-radius: 4px;
  font-weight: 500;  text-align: center;
  cursor: pointer;  transition: background-color 0.3s, transform 0.1s;
}
.btn:hover {  transform: translateY(-1px);
}
.btn:active {  transform: translateY(0);
}
.btn-primary {  background-color: var(--primary-color);
  color: white;}
.btn-primary:hover {
  background-color: #000088;}
.btn-secondary {
  background-color: var(--secondary-color);  color: white;
}
.btn-secondary:hover {  background-color: #1765cc;
}
/* Yardımcı sınıflar */.text-center {
  text-align: center;}
.text-right {
  text-align: right;}
.text-left {
  text-align: left;}
.text-primary {
  color: var(--primary-color);}
.text-secondary {
  color: var(--secondary-color);}
.bg-primary {
  background-color: var(--primary-color);}
.bg-secondary {
  background-color: var(--secondary-color);}
.font-thin {
  font-weight: 100;}
.font-light {
  font-weight: 300;}
.font-normal {
  font-weight: 400;}
.font-medium {
  font-weight: 500;}
.font-bold {
  font-weight: 700;}
.font-heavy {
  font-weight: 800;}
.font-black {
  font-weight: 900;}
.font-italic {
  font-style: italic;}
.font-primary {
  font-family: var(--font-family-primary);}
.font-secondary {
  font-family: var(--font-family-secondary);}
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }.mt-3 { margin-top: 1rem; }
.mt-4 { margin-top: 1.5rem; }.mt-5 { margin-top: 3rem; }
.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }.mb-3 { margin-bottom: 1rem; }
.mb-4 { margin-bottom: 1.5rem; }.mb-5 { margin-bottom: 3rem; }
.ml-1 { margin-left: 0.25rem; }
.ml-2 { margin-left: 0.5rem; }.ml-3 { margin-left: 1rem; }
.ml-4 { margin-left: 1.5rem; }.ml-5 { margin-left: 3rem; }
.mr-1 { margin-right: 0.25rem; }
.mr-2 { margin-right: 0.5rem; }.mr-3 { margin-right: 1rem; }
.mr-4 { margin-right: 1.5rem; }.mr-5 { margin-right: 3rem; }
.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }.p-3 { padding: 1rem; }
.p-4 { padding: 1.5rem; }.p-5 { padding: 3rem; }
.w-100 { width: 100%; }
.h-100 { height: 100%; }
.d-flex { display: flex; }.flex-column { flex-direction: column; }
.justify-content-center { justify-content: center; }.justify-content-between { justify-content: space-between; }
.align-items-center { align-items: center; }.flex-wrap { flex-wrap: wrap; }
/* Responsive */
@media (max-width: 768px) {  h1 { font-size: 2rem; }
  h2 { font-size: 1.75rem; }  h3 { font-size: 1.5rem; }
  h4 { font-size: 1.25rem; }  h5 { font-size: 1.1rem; }
  h6 { font-size: 1rem; }
}
























































































































































































































