.signup-container {
  min-height: 100vh;
  display: flex;
  overflow: hidden;
}

.signup-content {
  display: flex;
  width: 100%;
}

.signup-form-column {
  width: 40%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background-color: #fff;
  padding: 40px;
}

.signup-form-container {
  max-width: 600px;
  margin: 0 auto;
  width: 100%;
  padding: 20px;
  text-align: left;
}

.signup-logo {
  width: 100px;
  margin-bottom: 30px;
}

.signup-title {
  font-size: 28px;
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.signup-subtitle {
  font-size: 28px;
  font-weight: 500;
  color: #000066;
  margin-bottom: 30px;
}

.signup-input-group {
  margin-bottom: 20px;
}

.signup-label {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.signup-input {
  width: 100%;
  padding: 12px 16px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  background-color: #f5f5f5;
  font-size: 16px;
  transition: border-color 0.3s;
}

.signup-input:focus {
  border-color: #000066;
  outline: none;
}

.signup-input::placeholder {
  color: #aaa;
}

.signup-continue-btn {
  width: 100%;
  padding: 12px 16px;
  background-color: #000066;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
  margin-bottom: 20px;
}

.signup-continue-btn:hover {
  background-color: #000088;
}

.continue-btn-content {
  display: flex;
  align-items: center;
  justify-content: center;
}

.continue-icon {
  margin-left: 8px;
}

.signup-divider {
  position: relative;
  text-align: center;
  margin: 20px 0;
}

.signup-divider::before,
.signup-divider::after {
  content: "";
  position: absolute;
  top: 50%;
  width: 45%;
  height: 1px;
  background-color: #e0e0e0;
}

.signup-divider::before {
  left: 0;
}

.signup-divider::after {
  right: 0;
}

.divider-text {
  display: inline-block;
  padding: 0 10px;
  background-color: white;
  position: relative;
  color: #666;
  font-size: 14px;
}

.signup-sso-btn {
  width: 100%;
  padding: 12px 16px;
  background-color: #1a73e8;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.signup-sso-btn:hover {
  background-color: #1765cc;
}

.signup-footer {
  margin-top: 40px;
  text-align: center;
  font-size: 14px;
  color: #666;
}

.login-link {
  color: #000066;
  text-decoration: none;
  font-weight: 500;
}

.login-link:hover {
  text-decoration: underline;
}

.signup-image-column {
  width: 60%;
  background-color: #000066;
  position: relative;
}

.image-container {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-image: url('https://cdn.builder.io/api/v1/image/assets/TEMP/dcf80d9b74e5872fd282ae4c1988529aa0b1e5f1?placeholderIfAbsent=true');
  background-size: cover;
  background-position: center;
  position: relative;
}

.testimonial-overlay {
  background-color: rgba(0, 0, 102, 0.85);
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.testimonial-card {
  background-color: white;
  border-radius: 8px;
  padding: 30px;
  max-width: 600px;
  position: relative;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding-bottom: 0;
}

.quote-mark {
  position: absolute;
  top: 20px;
  left: 20px;
  font-size: 60px;
  color: #000066;
  font-family: Georgia, serif;
  line-height: 0.6;
}

.testimonial-content {
  padding-top: 10px;
  display: flex;
}

.testimonial-text {
  font-size: 16px;
  line-height: 1.6;
  color: #333;
  margin-bottom: 20px;
  text-align: left;
  width: 60%;
}

.testimonial-author {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40%;
}

.author-info {
  flex: 1;
}

.author-name {
  font-weight: 600;
  color: #333;
  font-size: 16px;
}

.author-title {
  color: #666;
  font-size: 14px;
}

.author-image-container {
  /* width: 80px;
  height: 80px;
  overflow: hidden;
  border-radius: 4px; */
}

.author-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

@media (max-width: 991px) {
  .signup-content {
    flex-direction: column;
  }

  .signup-form-column,
  .signup-image-column {
    width: 100%;
  }

  .signup-form-column {
    order: 1;
    padding: 20px;
  }

  .signup-image-column {
    order: 0;
    min-height: 300px;
  }

  .testimonial-card {
    max-width: 100%;
    padding: 20px;
  }

  .testimonial-author {
    flex-direction: column;
    align-items: flex-start;
  }

  .author-image-container {
    margin-top: 10px;
  }
}




