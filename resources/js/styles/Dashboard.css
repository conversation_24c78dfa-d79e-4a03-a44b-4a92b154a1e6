/* Dashboard Layout */
.dashboard {
  min-height: 100vh;
  background-color: #f8fafc;
  font-family: Inter, sans-serif;
}

.dashboard-content {
  padding: 1rem 1.5rem;
}

/* Header Styles */
.main-header {
  background-color: #ffffff;
  border-bottom: 1px solid #e2e8f0;
  padding: 15px;
  padding-bottom: 0;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.brand {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.brand-logo {
  color: #ffffff;
  font-size: 0.88rem;
  font-weight: 700;
  width: 2rem;
  height: 2rem;
  background-color: #2563eb;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.signup-logo {
    width: 100px;
    margin-bottom: 0px;
}

.brand-name {
  color: #1e293b;
  font-size: 1.13rem;
  font-weight: 600;
}

.main-nav {
  display: flex;
  align-items: center;
  gap: 2rem;
}

.nav-item {
  color: #64748b;
  font-size: 0.88rem;
  font-weight: 500;
  padding-bottom: 1rem;
  cursor: pointer;
}

.nav-item.active {
    font-style: normal;
    font-weight: bold;
    font-size: 16px;
    line-height: 26px;
    color: #000067;
    border-bottom: 4px solid #000067;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.icon-button {
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 8px;
}

.icon-button:hover {
  opacity: 0.8;
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: #8B5CF6;
  overflow: hidden;
  text-decoration: none;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* Notification Banner */
.notification-banner {
  background-color: #ede9fe;
  border: 1px solid #c4b5fd;
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.notification-message {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.notification-icon {
  color: #7c3aed;
  font-size: 1.13rem;
}

.notification-message span {
  color: #5b21b6;
  font-size: 0.88rem;
}

.notification-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.notification-demo-btn {
  color: #7c3aed;
  font-size: 0.88rem;
  font-weight: 500;
  background: none;
  border: none;
  cursor: pointer;
}

.notification-close {
  color: #7c3aed;
  font-size: 1.13rem;
  cursor: pointer;
}

/* Dashboard Header */
.dashboard-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 2rem;
}

.dashboard-title {
  color: #1e293b;
  font-size: 1.5rem;
  font-weight: 600;
}

.create-dashboard-btn {
  background-color: #1e40af;
  color: #ffffff;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  font-size: 0.88rem;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  border: none;
  cursor: pointer;
}

/* Templates Section */
.templates-section {
  margin-bottom: 2rem;
}

.templates-title {
  color: #64748b;
  font-size: 0.88rem;
  font-weight: 500;
  margin-bottom: 1rem;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 1rem;
}

.template-card {
  background-color: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1.5rem;
  text-align: center;
}

.template-icon-wrapper {
  width: 3rem;
  height: 3rem;
  background-color: #dbeafe;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1rem;
}

.template-icon {
  color: #2563eb;
  font-size: 1.25rem;
}

.template-title {
  color: #1e293b;
  font-size: 0.88rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

.template-stats {
  color: #64748b;
  font-size: 0.75rem;
}

.template-card-more .template-icon-wrapper {
  background-color: #f1f5f9;
}

.template-card-more .template-icon {
  color: #64748b;
}

.template-title-more {
  color: #64748b;
  font-size: 0.88rem;
  font-weight: 500;
  margin-bottom: 0.5rem;
}

/* Table Styles */
.dashboard-table-container {
  background-color: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
}

.table-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.table-title {
  color: #1e293b;
  font-size: 1.13rem;
  font-weight: 500;
}

.table-actions {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.filter-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 0.88rem;
  background: none;
  border: none;
  cursor: pointer;
}

.table-view-icon {
  color: #64748b;
  font-size: 1.13rem;
  cursor: pointer;
}

.table-wrapper {
  overflow-x: auto;
}

.dashboard-table {
  width: 100%;
  border-collapse: collapse;
}

.dashboard-table th {
  text-align: left;
  padding: 1rem 1.5rem;
  color: #64748b;
  font-size: 0.88rem;
  font-weight: 500;
  border-bottom: 1px solid #e2e8f0;
}

.dashboard-table td {
  padding: 1rem 1.5rem;
  border-bottom: 1px solid #e2e8f0;
}

.board-name {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.board-icon-wrapper {
  width: 2rem;
  height: 2rem;
  background-color: #dbeafe;
  border-radius: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.board-icon {
  color: #2563eb;
  font-size: 0.88rem;
}

.board-title {
  color: #1e293b;
  font-size: 0.88rem;
  font-weight: 500;
}

.board-brands {
  color: #64748b;
  font-size: 0.88rem;
}

.board-user-type {
  color: #1e293b;
  font-size: 0.88rem;
}

.board-actions {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.action-icon {
  color: #64748b;
  font-size: 1.13rem;
  cursor: pointer;
}

/* Pagination */
.table-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1.5rem;
  border-top: 1px solid #e2e8f0;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.pagination-btn {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #64748b;
  font-size: 0.88rem;
  border-radius: 0.25rem;
  border: none;
  background: none;
  cursor: pointer;
}

.pagination-btn.active {
  background-color: #1e40af;
  color: #ffffff;
  font-weight: 500;
}

.pagination-ellipsis {
  color: #64748b;
  font-size: 0.88rem;
  padding: 0 0.5rem;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .templates-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .templates-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .main-nav {
    display: none;
  }
}

@media (max-width: 640px) {
  .templates-grid {
    grid-template-columns: 1fr;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }

  .table-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
}

/* Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.template-modal {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h2 {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.25rem;
  color: #64748b;
}

.template-options {
  padding: 16px 24px;
}

.template-option {
  display: flex;
  padding: 24px 0;
  border-bottom: 1px solid #e2e8f0;
}

.template-option:last-child {
  border-bottom: none;
}

.template-preview {
  flex: 0 0 200px;
  margin-right: 24px;
  position: relative;
}

.template-preview img {
  width: 100%;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.template-type {
  position: absolute;
  top: 12px;
  left: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  color: #1e293b;
}

.template-details {
  flex: 1;
  text-align: left;
}

.template-details h3 {
  margin: 0 0 8px;
  font-size: 1.125rem;
  font-weight: 600;
  color: #1e293b;
}

.template-details p {
  margin: 0 0 16px;
  font-size: 0.875rem;
  color: #64748b;
}

.template-actions {
  display: flex;
  gap: 12px;
}

.use-template-btn {
  background-color: #000067;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 8px 16px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
}

.view-demo-btn {
  background-color: transparent;
  color: #000067;
  border: none;
  padding: 8px 16px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
}

.modal-footer {
  padding: 16px 24px;
  text-align: center;
  border-top: 1px solid #e2e8f0;
}

.show-more-btn {
  background-color: transparent;
  color: #000067;
  border: none;
  padding: 8px 16px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
}

/* Template Form Modal Styles */
.template-form-modal {
  background-color: white;
  border-radius: 8px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  /* overflow-y: auto; */
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.template-form-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  border-bottom: 1px solid #e2e8f0;
}

.template-form-modal .back-button {
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: transparent;
  border: none;
  padding: 8px 0;
  font-size: 0.875rem;
  color: #475569;
  cursor: pointer;
}

.template-form-modal .close-button {
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.25rem;
  color: #64748b;
}

.template-form-content {
  padding: 24px;
}

.template-form-steps {
  display: flex;
  margin-bottom: 16px;
  gap: 24px;
}

.template-form-steps .step {
  font-size: 0.875rem;
  color: #64748b;
  padding-bottom: 8px;
  position: relative;
}

.template-form-steps .step.active {
  color: #000067;
  font-weight: 600;
}

.template-form-steps .step.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #000067;
  border-radius: 2px;
}

.template-form-steps .step-number {
  font-weight: 600;
}

.step-indicator {
  display: none;
}

.form-group-gray {
  background-color: #f8fafc;
  padding: 16px;
  border-radius: 8px;
  margin-bottom: 16px;
}

.form-group-gray .form-group {
  display: flex;
  gap: 16px;
  margin-bottom: 0;
}

.form-group-gray .form-control {
  flex: 1;
  background-color: white;
}

.form-group-gray .brand-selector {
  flex: 1;
  background-color: white;
  border: 1px dashed #cbd5e1;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border-radius: 4px;
  color: #64748b;
}

.template-form-step p{
  text-align: left;
}

.template-form-step .form-control {
  width: 100%;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 0.875rem;
  background-color: white;
}

.template-form-step .form-control:focus {
  outline: none;
  border-color: #000067;
  box-shadow: 0 0 0 2px rgba(0, 0, 103, 0.1);
}

.form-section {
  margin-top: 32px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.section-header h4 {
  font-size: 0.875rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.event-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
}

.add-event-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 1px dashed #cbd5e1;
  border-radius: 4px;
  background-color: #f8fafc;
  color: #64748b;
  cursor: pointer;
  width: 48%;
  font-size: 0.875rem;
}

.add-event-btn:hover {
  background-color: #f1f5f9;
}

.event-input-container {
  width: 48%;
}

.event-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #000067;
  border-radius: 4px;
  font-size: 0.875rem;
  background-color: white;
}

.event-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 0, 103, 0.1);
}

.event-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 1px dashed #cbd5e1;
  border-radius: 4px;
  background-color: #f8fafc;
  color: #64748b;
  width: 48%;
  font-size: 0.875rem;
}

.value-action {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 1px dashed #cbd5e1;
  border-radius: 4px;
  background-color: #f8fafc;
  color: #64748b;
  width: 48%;
  font-size: 0.875rem;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 32px;
}

.back-btn {
  background-color: transparent;
  color: #64748b;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  padding: 12px 24px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
}

.back-btn:hover {
  background-color: #f8fafc;
}

.next-btn {
  background-color: #000067;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 12px 24px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
}

.next-btn:hover {
  background-color: #000088;
}

/* Event Dropdown Styles */
.event-dropdown-container {
  position: relative;
  width: 48%;
}

.event-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
  margin-top: 8px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
}

.event-dropdown-search {
  padding: 12px;
  border-bottom: 1px solid #f1f5f9;
  position: relative;
}

.search-icon {
  position: absolute;
  left: 20px;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
}

.event-search-input {
  width: 100%;
  padding: 8px 8px 8px 32px;
  border: 1px solid #e2e8f0;
  border-radius: 20px;
  font-size: 0.875rem;
  background-color: #f8fafc;
}

.event-search-input:focus {
  outline: none;
  border-color: #000067;
  box-shadow: 0 0 0 2px rgba(0, 0, 103, 0.1);
}

.event-dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  font-size: 0.875rem;
}

.event-dropdown-header span {
  color: #1e293b;
  font-weight: 500;
}

.custom-event-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  color: #000067;
  font-size: 0.875rem;
  cursor: pointer;
}

.event-dropdown-list {
  padding: 0 0 12px;
}

.event-dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  font-size: 0.875rem;
  color: #1e293b;
  cursor: pointer;
}

.event-dropdown-item:hover {
  background-color: #f8fafc;
}

.event-dropdown-item i {
  color: #64748b;
}

.add-event-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 1px dashed #cbd5e1;
  border-radius: 4px;
  background-color: #f8fafc;
  color: #64748b;
  cursor: pointer;
  width: 100%;
  font-size: 0.875rem;
}

/* KPI Styles */
.kpi-section {
  margin-top: 24px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.kpi-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  background-color: #f8fafc;
  color: #1e293b;
  font-size: 0.875rem;
}

.kpi-item i {
  color: #64748b;
}

.kpi-dropdown-container {
  position: relative;
  width: 100%;
}

.add-kpi-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 1px dashed #cbd5e1;
  border-radius: 4px;
  background-color: #f8fafc;
  color: #64748b;
  cursor: pointer;
  width: 100%;
  font-size: 0.875rem;
}

.add-kpi-btn:hover {
  background-color: #f1f5f9;
}

.kpi-input-container {
  width: 100%;
}

.kpi-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #000067;
  border-radius: 4px;
  font-size: 0.875rem;
  background-color: white;
}

.kpi-input:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(0, 0, 103, 0.1);
}

.kpi-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
  margin-top: 8px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
}

.kpi-dropdown-search {
  padding: 12px;
  border-bottom: 1px solid #f1f5f9;
  position: relative;
}

.kpi-search-input {
  width: 100%;
  padding: 8px 8px 8px 32px;
  border: 1px solid #e2e8f0;
  border-radius: 20px;
  font-size: 0.875rem;
  background-color: #f8fafc;
}

.kpi-search-input:focus {
  outline: none;
  border-color: #000067;
  box-shadow: 0 0 0 2px rgba(0, 0, 103, 0.1);
}

.kpi-dropdown-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  font-size: 0.875rem;
}

.kpi-dropdown-header span {
  color: #1e293b;
  font-weight: 500;
}

.custom-kpi-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  background: none;
  border: none;
  color: #000067;
  font-size: 0.875rem;
  cursor: pointer;
}

.kpi-dropdown-list {
  padding: 0 0 12px;
}

.kpi-dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  font-size: 0.875rem;
  color: #1e293b;
  cursor: pointer;
}

.kpi-dropdown-item:hover {
  background-color: #f8fafc;
}

.form-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 32px;
}

.back-btn {
  background-color: transparent;
  color: #64748b;
  border: 1px solid #cbd5e1;
  border-radius: 4px;
  padding: 12px 24px;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
}

.back-btn:hover {
  background-color: #f8fafc;
}

.template-form-steps {
  display: flex;
  margin-bottom: 16px;
  gap: 24px;
}

.template-form-steps .step {
  font-size: 0.875rem;
  color: #64748b;
  padding-bottom: 8px;
  position: relative;
}

.template-form-steps .step.active {
  color: #000067;
  font-weight: 600;
}

.template-form-steps .step.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 3px;
  background-color: #000067;
  border-radius: 2px;
}

/* Authorization Styles */
.authorization-section {
  background-color: #ffffff;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-top: 1.5rem;
}

.invite-users-container {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.invite-users-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.invite-users-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.close-invite-btn {
  background: none;
  border: none;
  color: #64748b;
  font-size: 1.25rem;
  cursor: pointer;
}

.invite-form {
  margin-bottom: 1rem;
}

.invite-input-row {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.invite-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  background-color: #f8fafc;
}

.invite-input:focus {
  outline: none;
  border-color: #000067;
  box-shadow: 0 0 0 2px rgba(0, 0, 103, 0.1);
}

.permission-select {
  position: relative;
}

.permission-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  border: 1px solid #e2e8f0;
  border-radius: 0.375rem;
  background-color: #f8fafc;
  color: #1e293b;
  font-size: 0.875rem;
  cursor: pointer;
  white-space: nowrap;
}

.send-invite-btn {
  padding: 0.75rem 1.5rem;
  background-color: #000067;
  color: white;
  border: none;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  white-space: nowrap;
}

.current-users {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.user-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
}

.user-info-container {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.user-avatar {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  overflow: hidden;
  background-color: #6366f1;
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-info {
  flex: 1;
}

.user-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #1e293b;
}

.user-email {
  font-size: 0.75rem;
  color: #64748b;
}

.user-role {
  font-size: 0.875rem;
  color: #64748b;
}

.access-settings {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
  border-top: 1px solid #e2e8f0;
  border-bottom: 1px solid #e2e8f0;
}

.access-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: #1e293b;
}

.access-option i {
  color: #64748b;
  font-size: 1rem;
}

.share-link {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.copy-link-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: none;
  border: none;
  color: #000067;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
}

.learn-more {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #64748b;
  font-size: 0.875rem;
  text-decoration: none;
}

.learn-more i {
  font-size: 1rem;
}

/* Invite Input Dropdown Styles */
.invite-input-wrapper {
  position: relative;
  flex: 1;
}

.users-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background-color: white;
  border-radius: 0.375rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
  margin-top: 0.5rem;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
}

.user-dropdown-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  cursor: pointer;
}

.user-dropdown-item:hover {
  background-color: #f8fafc;
}

/* Authorization Card Styles */
.authorization-card {
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  padding: 24px;
  margin-top: 24px;
  border: 1px solid #e2e8f0;
}

.auth-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.auth-card-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
  margin: 0;
}

.close-auth-btn {
  background: none;
  border: none;
  color: #64748b;
  font-size: 18px;
  cursor: pointer;
}

.auth-input-row {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.auth-input {
  flex: 1;
  padding: 10px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 14px;
  background-color: #f8fafc;
}

.auth-input:focus {
  outline: none;
  border-color: #000067;
}

.auth-permission-select {
  position: relative;
}

.auth-permission-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  background-color: #f8fafc;
  color: #1e293b;
  font-size: 14px;
  cursor: pointer;
  white-space: nowrap;
}

.auth-invite-btn {
  padding: 10px 20px;
  background-color: #000067;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  white-space: nowrap;
}

.auth-users-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  width: 100%;
  background-color: white;
  border-radius: 4px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 10;
  margin-top: 4px;
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e2e8f0;
}

.auth-user-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
}

.auth-user-item:hover {
  background-color: #f8fafc;
}

.auth-user-list {
  margin-bottom: 16px;
}

.auth-user-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.auth-user-info-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.auth-user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  background-color: #6366f1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 14px;
}

.avatar-text {
  font-weight: 500;
}

.auth-user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.auth-user-info {
  flex: 1;
}

.auth-user-name {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
}

.auth-user-email {
  font-size: 12px;
  color: #64748b;
}

.auth-user-role {
  font-size: 14px;
  color: #64748b;
}

.auth-divider {
  height: 1px;
  background-color: #e2e8f0;
  margin: 16px 0;
}

.auth-access-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.auth-access-option {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 14px;
  color: #1e293b;
}

.auth-access-option i {
  color: #64748b;
  font-size: 16px;
}

.auth-link-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.auth-copy-link-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  color: #000067;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
}

.auth-learn-more {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #64748b;
  font-size: 14px;
  text-decoration: none;
}

.auth-learn-more i {
  font-size: 16px;
}

/* Saving Options Styles */
.saving-options-container {
  margin-top: 24px;
}

.saving-options-title {
  font-size: 16px;
  font-weight: 500;
  color: #1e293b;
  margin-bottom: 8px;
}

.saving-options-subtitle {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 32px;
}

.saving-options-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.saving-option {
  display: grid;
  grid-template-columns: 1fr 2fr 1fr;
  gap: 16px;
  align-items: flex-start;
}

.saving-option-radio {
  display: flex;
  align-items: center;
  gap: 8px;
}

.saving-option-radio input[type="radio"] {
  width: 18px;
  height: 18px;
  accent-color: #000067;
}

.saving-option-radio label {
  font-size: 14px;
  font-weight: 500;
  color: #1e293b;
  text-align: left;
}

.saving-option-description p {
  font-size: 14px;
  color: #64748b;
  margin: 0;
}

.saving-option-users p {
  font-size: 14px;
  color: #64748b;
  margin: 0;
}

.complete-btn {
  background-color: #000067;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 12px 24px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  text-transform: uppercase;
}

.complete-btn:hover {
  background-color: #000088;
}



