import React, { useState } from "react";
// import { useNavigate } from "react-router";
import "@/styles/general.css";
import "@/styles/SignUp.css";

function SignUp() {
  const [email, setEmail] = useState("");
  // const navigate = useNavigate();

  const handleEmailChange = (e) => {
    setEmail(e.target.value);
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    console.log("Form submitted with email:", email);
    // Dashboard sayfasına yönlendir
    // navigate("/dashboard");
  };

  return (
    <div className="signup-container">
      <div className="signup-content">
        <div className="signup-form-column">
          <div className="signup-form-container">
            <img
              src="https://cdn.builder.io/api/v1/image/assets/TEMP/ff79270abae4069c92179445e6e8601c1e5c3f7c?placeholderIfAbsent=true"
              className="signup-logo"
              alt="Pfizer Logo"
            />
            <div className="signup-title">Sign in to continue to</div>
            <div className="signup-subtitle">SmartRX</div>

            <form onSubmit={handleSubmit}>
              <div className="signup-input-group">
                <label className="signup-label" htmlFor="email">Email</label>
                <input
                  id="email"
                  type="email"
                  className="signup-input"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={handleEmailChange}
                  required
                />
              </div>

              <button type="submit" className="signup-continue-btn">
                <div className="continue-btn-content">
                  <span>Continue</span>
                  <svg className="continue-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M12 4L10.59 5.41L16.17 11H4V13H16.17L10.59 18.59L12 20L20 12L12 4Z" fill="white" />
                  </svg>
                </div>
              </button>
            </form>

            <div className="signup-divider">
              <span className="divider-text">OR</span>
            </div>

            <button type="button" className="signup-sso-btn">Pfizer SSO</button>

            <div className="signup-footer">
              Already have an account?{" "}
              <a href="/login" className="login-link">Log in</a>
            </div>
          </div>
        </div>

        <div className="signup-image-column">
          <div className="image-container">
            <div className="testimonial-overlay">
              <div className="testimonial-card">
                <div className="quote-mark">"</div>
                <div className="testimonial-content">
                  <div className="testimonial-text">
                    Access real-time analytics, campaign insights, and AI-powered recommendations — all in one secure platform tailored for your team. Pfizer SmartXR is now available for leadership, marketing and media teams!
                    <div className="author-info">
                      <div className="author-name">Kozhin Fatah</div>
                      <div className="author-title">Technical Delivery Manager, OSS</div>
                    </div>
                  </div>
                  <div className="testimonial-author">
                    <img
                      src="https://cdn.builder.io/api/v1/image/assets/TEMP/d33ebb1730532211dedbc8ba796a9d96d6a8dc77?placeholderIfAbsent=true"
                      className="author-image"
                      alt="Author"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default SignUp;





